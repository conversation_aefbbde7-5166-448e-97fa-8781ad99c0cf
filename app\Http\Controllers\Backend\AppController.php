<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\AppModel;
use App\Models\AppCategory;
use App\Http\Requests\Backend\DomainWhitelist;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Validator;

class AppController extends Controller
{
    
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $data = AppModel::orderBy('id', 'desc')->paginate(20);        
        return view('backend.apps.index', compact('data'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
		$categories = AppCategory::pluck('name', 'id')->toArray();
        return view('backend.apps.create', compact('categories'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
			'category_id' => 'required|exists:app_categories,id',
            'application' => 'required|string',
            'package' => 'required|string|unique:apps,package',
            'icon' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
		]);

		if ($validator->fails()) {
			return back()->withErrors($validator)->withInput();
		}

        $input = $request->only(['category_id','application','package']);
		
		if ($request->hasFile('icon')) {
			$input['icon'] = $this->uploadFile($request->file('icon'), 'app-icons');
		}
        
        AppModel::create($input);
        
        return redirect()->route('admin.app')->with('alert-success', 'Record has been created successfully.');
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $user = AppModel::find($id);
		$categories = AppCategory::pluck('name', 'id')->toArray();
        return view('backend.apps.edit', compact('user','categories'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
		$validator = Validator::make($request->all(), [
			'category_id' => 'required',
            'application' => 'required|string',
            'package' => 'required|string',
            'icon' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
		]);

		if ($validator->fails()) {
			return back()->withErrors($validator)->withInput();
		}
		
        $user = AppModel::find($id);
		
        $input = $request->only(['category_id','application','package']);
		
		if ($request->hasFile('icon')) {
			$input['icon'] = $this->uploadFile($request->file('icon'), 'app-icons');
		}
		
        $user->update($input);
        
        return redirect()->route('admin.app')->with('alert-success', 'Record updated successfully.');
    }
	
	/**
	 * Uploads a file with a timestamp-based name
	 */
	private function uploadFile($file, $path)
	{
		$fileName = md5($file->getFilename() . time()) . '.' . $file->getClientOriginalExtension();
		$file->storeAs($path, $fileName, 'public');
		
		return $fileName;
	}

    
}

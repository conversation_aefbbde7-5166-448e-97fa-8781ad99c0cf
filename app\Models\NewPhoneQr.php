<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class NewPhoneQr extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'new_phone_qr';
	
	protected $dates = ['deleted_at'];
	
	protected $fillable = [
		'company_id',
		'image',
		'imei',
		'retailer_mobile',
		'status'
	];

	public function device()
	{
		return $this->hasOne(DeviceInfo::class);
	}
	
	public function company()
	{
		return $this->belongsTo(ApiKey::class, 'company_id');
	}
}

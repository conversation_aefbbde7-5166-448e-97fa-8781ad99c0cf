<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Database\Eloquent\SoftDeletes;

class Customer extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'retailer_id', 'name', 'email', 'mobile', 'customer_photo', 'address', 'plan','device_id', 'model', 'manufacture',
        'id_proof', 'product', 'device_type', 'imei_1', 'imei_2', 'customer_sign', 
        'retailer_sign', 'financer_name', 'product_price', 'downpayment', 'balance_amount', 
        'emi_duration', 'num_of_months_weeks', 'interest_rate', 'total_emi', 
        'monthly_weekly_emi', 'country', 'state', 'city', 'reg_date','device_status','firebase_token','default_password',
		'suspend_apps','retailer_mobile','owner_mobile','is_camera_enabled', 'is_locked', 'is_usb_enabled','is_disable_format',
		'is_wallpaper_enabled','system_apps','hide_apps','secondary_contacts','is_connected'
    ];

    public function retailer()
    {
        return $this->belongsTo(User::class, 'retailer_id');
    }
	
	/**
     * Relationship with CustomerDefaultPassword
     * A customer can have multiple default passwords.
     */
    public function defaultPasswords()
    {
        return $this->hasMany(CustomerDefaultPassword::class)
			->where('customer_id', $this->getOriginal('id'));
    }
	
	public function emergencyContacts()
	{
		return $this->hasMany(CustomerEmergencyContact::class, 'customer_id');
	}
	
	// Encrypt customer ID when returning data
    public function getIdAttribute($value)
    {
        return Crypt::encryptString($value);
    }
	
	public function countryDetail()
	{
		return $this->belongsTo(Country::class, 'country');
	}

	public function stateDetail()
	{
		return $this->belongsTo(State::class, 'state');
	}

	public function cityDetail()
	{
		return $this->belongsTo(City::class, 'city');
	}
	
	// Accessors for Full File URLs
    public function getCustomerPhotoAttribute($value)
    {
        return $value ? config('app.url').Storage::url('customers/photo/'.$value) : null;
    }

    public function getIdProofAttribute($value)
    {
        return $value ? config('app.url').Storage::url('customers/id-proof/'.$value) : null;
    }

    public function getCustomerSignAttribute($value)
    {
        return $value ? config('app.url').Storage::url('customers/customer-sign/'.$value) : null;
    }

    public function getRetailerSignAttribute($value)
    {
        return $value ? config('app.url').Storage::url('customers/retailer-sign/'.$value) : null;
    }
}

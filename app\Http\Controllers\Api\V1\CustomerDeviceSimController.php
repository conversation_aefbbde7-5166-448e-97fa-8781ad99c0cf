<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\CustomerDeviceSim;
use App\Models\Customer;
use Illuminate\Support\Facades\Crypt;
use App\Services\ImeiActivityService;
use Carbon\Carbon;

class CustomerDeviceSimController extends Controller
{
    // Store SIM info
    public function store(Request $request)
    {
        $request->validate([
            'imei_1' => 'required|string',
            'mobile_1' => 'required|string|max:15',
            'mobile_2' => 'nullable|string|max:15',
        ]);

        try {
			
			$imei = $request->imei_1;
			
            // ✅ Check in `customers` table
            $customer = Customer::with('retailer:id,username,email,mobile,plan')
				->where('imei_1', $imei)
                ->select('id', 'retailer_id', 'firebase_token', 'device_status')
                ->first();

            if (!$customer) {
                return $this->sendError('Customer does not exist. Please contact admin.', '', 200);
            }
			
			$customerId = Crypt::decryptString($customer->id);
			
			// Get count of today's entries for this customer
			$countToday = CustomerDeviceSim::where('customer_id', $customerId)
				->whereDate('created_at', today())
				->count();

			if ($countToday >= 10) {
				return response()->json(['message' => 'Daily limit reached (10 entries per day)'], 200);
			}
			
			$activityData = [
				'imei' => $request->imei_1,
				'request' => $request->all()
			];
            ImeiActivityService::log('Add Sim', auth()->user(), null, $activityData, 'customer');

            // Store SIM details
            $sim = CustomerDeviceSim::create([
                'customer_id' => $customerId,
                'mobile_1' => $request->mobile_1,
                'mobile_2' => $request->mobile_2,
            ]);

            return response()->json(['message' => 'SIM details stored successfully', 'data' => $sim], 200);
        } catch (\Exception $e) {
			$activityData = [
				'imei' => $request->imei_1,
				'request' => $e->getMessage()
			];
            ImeiActivityService::log('Add Sim Error', auth()->user(), null, $activityData, 'customer');
			
            return response()->json(['message' => $e->getMessage()], 200);
        }
    }

    // Get SIM info
    public function index($encryptedId, Request $request)
    {
        try {
			$id = Crypt::decryptString($encryptedId);
		} catch (\Exception $e) {
			return response()->json(['message' => 'Invalid ID'], 200);
		}
		
        // Get date from request or default to today's date
		$date = $request->input('date', Carbon::now()->toDateString());

		$sims = CustomerDeviceSim::with('customer')
			->select('id', 'mobile_1', 'mobile_2', 'created_at')
			->whereNull('deleted_at')
			->whereDate('created_at', $date)
			->paginate(10);

		return response()->json($sims);
    }

    // Soft delete SIM info
    public function destroy($id)
    {
        try {
            $sim = CustomerDeviceSim::findOrFail($id);
            $sim->delete(); // Soft delete
            return response()->json(['message' => 'SIM details deleted successfully'], 200);
        } catch (\Exception $e) {
            return response()->json(['message' => 'SIM details not found'], 200);
        }
    }
}

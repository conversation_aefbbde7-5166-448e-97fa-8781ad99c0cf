<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Spatie\Permission\Models\Role;
use Illuminate\Support\Facades\Hash;

class UserRoleTestSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $roles = [            
            'national_distributor',
            'super_distributor',
            'distributor',            
        ];

        foreach ($roles as $roleName) {
            // Create or find the role
            $role = Role::firstOrCreate(['name' => $roleName]);

            // Create a test user for this role
            $user = User::firstOrCreate(
                ['email' => "{$roleName}@test.com"],
                [
                    'username' => ucwords(str_replace('_', ' ', $roleName)),
                    'password' => Hash::make('123456'), // default password
                ]
            );

            // Assign role
            $user->syncRoles([$roleName]);
        }
    }
}

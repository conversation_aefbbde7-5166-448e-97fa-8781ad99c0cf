@extends('backend.layouts.app')
@section('title', 'Manage User')
@section('content')
@php
$required = true;
@endphp
<div class="page-breadcrumb">
    <div class="row">
        <div class="col-5 align-self-center">
            <h4 class="page-title">{{ getRoleLabel($role, 'list') }}</h4>
        </div>
        <div class="col-7 align-self-center">
            <div class="d-flex align-items-center justify-content-end">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item">
                            <a href="{{ route('admin.dashboard')}}">Home</a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">{{ getRoleLabel($role, 'list') }}</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>
</div>
<!-- ============================================================== -->
<!-- Container fluid  -->
<!-- ============================================================== -->
<div class="container-fluid">
    <!-- ============================================================== -->
    <!-- Sales chart -->
    <!-- ============================================================== -->
    <div class="row">@include('backend.default_alert')</div>

    <!-- Enhanced Filter Section -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-filter"></i> Advanced Filters
                        <button type="button" class="btn btn-sm btn-outline-secondary float-right" id="toggleFilters">
                            <i class="fas fa-chevron-up"></i> Collapse
                        </button>
                    </h5>
                </div>
                <div class="card-body" id="filterSection">
                    <form action="{{ route('admin.users.index') }}" method="GET" class="w-100">
                        <div class="row">
                            <!-- Keyword Search -->
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label for="keyword" class="form-label">
                                        <i class="fas fa-search"></i> Search Keyword
                                    </label>
                                    <input type="text"
                                           name="keyword"
                                           id="keyword"
                                           class="form-control"
                                           placeholder="Name, Email, Mobile, Company..."
                                           value="{{ request('keyword') }}"
                                           title="Search by name, email, mobile number, or company name">
                                </div>
                            </div>

                            <!-- State Dropdown -->
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label for="state" class="form-label">
                                        <i class="fas fa-map-marker-alt"></i> State
                                    </label>
                                    {{ Form::select('state', ['' => 'All States'] + ($states ? $states->toArray() : []), request('state'), [
                                        'class' => 'form-control',
                                        'id' => 'filter_state_id'
                                    ]) }}
                                </div>
                            </div>

                            <!-- City Dropdown -->
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label for="city" class="form-label">
                                        <i class="fas fa-building"></i> City
                                    </label>
                                    {{ Form::select('city', ['' => 'All Cities'] + ($cities ? $cities->toArray() : []), request('city'), [
                                        'class' => 'form-control',
                                        'id' => 'filter_city_id'
                                    ]) }}
                                </div>
                            </div>

                            <!-- Date From -->
                            <div class="col-md-6 col-lg-3">
                                <div class="form-group">
                                    <label for="date_from" class="form-label">
                                        <i class="fas fa-calendar-alt"></i> Date From
                                    </label>
                                    <input type="date"
                                           name="date_from"
                                           id="date_from"
                                           class="form-control"
                                           value="{{ request('date_from') }}">
                                </div>
                            </div>

                            <!-- Date To -->
                            <div class="col-md-6 col-lg-3">
                                <div class="form-group">
                                    <label for="date_to" class="form-label">
                                        <i class="fas fa-calendar-alt"></i> Date To
                                    </label>
                                    <input type="date"
                                           name="date_to"
                                           id="date_to"
                                           class="form-control"
                                           value="{{ request('date_to') }}">
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="col-md-12 col-lg-6">
                                <div class="form-group">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-flex gap-2">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-search"></i> Search
                                        </button>
                                        <a href="{{ route('admin.users.index') }}" class="btn btn-secondary">
                                            <i class="fas fa-times"></i> Clear
                                        </a>
                                        <button type="button" class="btn btn-info" id="exportBtn">
                                            <i class="fas fa-download"></i> Export
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

   	<!-- Results Summary -->
    @if(request()->hasAny(['keyword', 'state', 'city', 'date_from', 'date_to']))
    <div class="row">
        <div class="col-12">
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i>
                <strong>Search Results:</strong> Found {{ $data->total() }} {{ $data->total() == 1 ? 'user' : 'users' }}
                @if(request('keyword'))
                    matching "<strong>{{ request('keyword') }}</strong>"
                @endif
                @if(request('state') && $states)
                    in state "<strong>{{ $states->get(request('state')) }}</strong>"
                @endif
                @if(request('city') && $cities)
                    in city "<strong>{{ $cities->get(request('city')) }}</strong>"
                @endif
                @if(request('date_from') || request('date_to'))
                    @if(request('date_from') && request('date_to'))
                        between {{ request('date_from') }} and {{ request('date_to') }}
                    @elseif(request('date_from'))
                        from {{ request('date_from') }}
                    @else
                        until {{ request('date_to') }}
                    @endif
                @endif
            </div>
        </div>
    </div>
    @endif

   	<!-- Users Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="card-title mb-0">
                            <i class="fas fa-users"></i> {{ getRoleLabel($role, 'list') }} Manager
                        </h5>
                        <small class="text-muted">Total: {{ $data->total() }} {{ $data->total() == 1 ? 'user' : 'users' }}</small>
                    </div>
                    <div>
                        <a class="btn btn-success" href="{{ route('admin.users.create') }}">
                            <i class="fa fa-plus"></i> {{ getRoleLabel($role, 'add') }}
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    @if($data->count() > 0)
                        <div class="table-responsive">
                            <table id="zero_config" class="table table-striped table-bordered table-hover">
                                <thead class="thead-light">
                                    <tr>
                                        <th width="5%">#</th>
                                        <th width="15%">
                                            <i class="fas fa-user"></i> Name
                                        </th>
                                        {{-- <th width="15%">
                                            <i class="fas fa-building"></i>Company Name
                                        </th> --}}
                                        {{-- <th width="15%">
                                            <i class="fas fa-envelope"></i> Email
                                        </th> --}}
                                        <th width="10%">
                                            <i class="fas fa-phone"></i> Mobile
                                        </th>
                                        <th width="15%">
                                            <i class="fas fa-map-marker-alt"></i> Location
                                        </th>
                                        <th width="10%">
                                            <i class="fas fa-tag"></i> Plan
                                        </th>
                                        <th width="8%">
                                            <i class="fas fa-key"></i> Keys
                                        </th>
                                        <th width="8%">
                                            <i class="fas fa-rupee-sign"></i> Price/Key
                                        </th>
                                        <th width="10%">
                                            <i class="fas fa-calendar"></i> Reg. Date
                                        </th>
                                        <th width="14%">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($data as $key => $user)
                                    <tr>
                                        <td>{{ ($data->currentPage() - 1) * $data->perPage() + $key + 1 }}</td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="user-avatar bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 35px; height: 35px; font-size: 14px;">
                                                    {{ strtoupper(substr($user->username, 0, 2)) }}
                                                </div>
                                                <div>
                                                    <strong>{{ $user->username }}</strong>
                                                    @if($user->company_name)
                                                        <br><small class="text-muted">{{ $user->company_name }}</small>
                                                    @endif
                                                </div>
                                            </div>
                                        </td>
                                        {{-- <td>
                                            <a href="mailto:{{ $user->email }}" class="text-decoration-none">
                                                {{ $user->email }}
                                            </a>
                                        </td> --}}
                                        <td>
                                            <a href="tel:{{ $user->mobile }}" class="text-decoration-none">
                                                {{ $user->mobile }}
                                            </a>
                                        </td>
                                        <td>
                                            <div>
                                                @if($user->stateDetail)
                                                    <i class="fas fa-map-marker-alt text-muted"></i> {{ $user->stateDetail->name }}
                                                @endif
                                                @if($user->cityDetail)
                                                    <br><small class="text-muted">{{ $user->cityDetail->name }}</small>
                                                @endif
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge badge-info">
                                                {{ ucwords(str_replace('_', ' ', $user->plan)) }}
                                            </span>
                                        </td>
                                        <td>
                                            <div class="text-center">
                                                <strong>{{ $user->total_keys }}</strong>
                                                <br><small class="text-muted">Used: {{ $user->used_keys ?? 0 }}</small>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="text-success font-weight-bold">
                                                ₹{{ number_format($user->price_per_key, 2) }}
                                            </span>
                                        </td>
                                        <td>
                                            <div>
                                                {{ date("M d, Y", strtotime($user->created_at)) }}
                                                {{-- <br><small class="text-muted">{{ $user->getRoleNames()->first() }}</small> --}}
                                            </div>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a class="btn btn-sm btn-outline-primary"
                                                   href="{{ route('admin.users.show', $user->id) }}"
                                                   title="View User">
                                                    <i class="fa fa-eye"></i>
                                                </a>
                                                <a class="btn btn-sm btn-outline-secondary"
                                                   href="{{ route('admin.users.edit', $user->id) }}"
                                                   title="Edit User">
                                                    <i class="fa fa-edit"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination with info -->
                        <div class="d-flex justify-content-between align-items-center mt-3">
                            <div class="pagination-info">
                                <small class="text-muted">
                                    Showing {{ $data->firstItem() ?? 0 }} to {{ $data->lastItem() ?? 0 }} of {{ $data->total() }} results
                                </small>
                            </div>
                            <div>
                                {{ $data->appends(request()->query())->links() }}
                            </div>
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No users found</h5>
                            <p class="text-muted">
                                @if(request()->hasAny(['keyword', 'state', 'city', 'date_from', 'date_to']))
                                    Try adjusting your search criteria or
                                    <a href="{{ route('admin.users.index') }}" class="text-primary">clear all filters</a>
                                @else
                                    <a href="{{ route('admin.users.create') }}" class="btn btn-primary">
                                        <i class="fa fa-plus"></i> Add your first {{ getRoleLabel($role, 'single') }}
                                    </a>
                                @endif
                            </p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
<!-- ============================================================== -->
<!-- End Container fluid  -->
<!-- ============================================================== -->
@endsection

@section('footer_js')
<script type="text/javascript">
    $(document).ready(function() {
        // Toggle filter section
        $('#toggleFilters').on('click', function() {
            var $filterSection = $('#filterSection');
            var $icon = $(this).find('i');

            if ($filterSection.is(':visible')) {
                $filterSection.slideUp();
                $icon.removeClass('fa-chevron-up').addClass('fa-chevron-down');
                $(this).html('<i class="fas fa-chevron-down"></i> Expand');
            } else {
                $filterSection.slideDown();
                $icon.removeClass('fa-chevron-down').addClass('fa-chevron-up');
                $(this).html('<i class="fas fa-chevron-up"></i> Collapse');
            }
        });

        // State-City dependency
        $('#filter_state_id').on('change', function() {
            var stateId = $(this).val();
            var $citySelect = $('#filter_city_id');

            // Clear city dropdown
            $citySelect.html('<option value="">All Cities</option>');

            if (stateId) {
                $.ajax({
                    url: "{{ route('admin.location.cities', '') }}/" + stateId,
                    type: 'GET',
                    dataType: 'json',
                    success: function(data) {
                        $.each(data, function(id, name) {
                            $citySelect.append('<option value="' + id + '">' + name + '</option>');
                        });

                        // Restore selected city if exists
                        var selectedCity = "{{ request('city') }}";
                        if (selectedCity) {
                            $citySelect.val(selectedCity);
                        }
                    },
                    error: function() {
                        console.log('Error loading cities');
                    }
                });
            }
        });

        // Trigger state change on page load if state is selected
        if ($('#filter_state_id').val()) {
            $('#filter_state_id').trigger('change');
        }

        // Export functionality
        $('#exportBtn').on('click', function() {
            var form = $(this).closest('form');
            var formData = form.serialize();
            window.open('{{ route("admin.users.index") }}?' + formData + '&export=1', '_blank');
        });

        // Date range validation
        $('#date_from, #date_to').on('change', function() {
            var dateFrom = $('#date_from').val();
            var dateTo = $('#date_to').val();

            if (dateFrom && dateTo && dateFrom > dateTo) {
                alert('Date From cannot be greater than Date To');
                $(this).val('');
            }
        });
    });

    $(document).on('click', '.updateStatus', function(event) {
    event.preventDefault();
    var _this = $(this);
    var title = _this.data('title');
    var value = _this.attr('data-value') == 1 ? 0 : 1 ;
    var column = _this.data('column');
    console.log(column);
    var message = "Are you sure want to active this user?";
    if(column == "status"){
        if(_this.attr('data-value') == 1){
            message = 'Are you sure want to in-active this user?';
        }else{
            message = 'Are you sure want to active this user?';
        }
    }
    $.confirm({
        title: 'Alert',
        content: message,
        icon: 'fa fa-exclamation-circle',
        animation: 'scale',
        closeAnimation: 'scale',
        opacity: 0.5,
        theme: 'supervan',
        buttons: {
            'confirm': {
                text: 'Yes',
                btnClass: 'btn-blue',
                action: function() {
                    $.ajax({
                        url: _this.data('url'),
                        data: {status: value},
                        type: 'POST',
                        dataType: "json",
                        headers: {
                            "accept": "application/json",
                            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                            },
                        success: function (data) {
                                if(data.success==true){
                                    if(value == 0){
                                        _this.removeClass("btn-success");
                                        _this.addClass("label-danger");
                                        _this.html("In-Active");
                                        _this.attr("data-value",0);
                                    }else{
                                        _this.removeClass("label-danger");
                                     //   _this.removeClass("updateStatus");
                                        _this.addClass("btn-success");
                                        _this.html("Active");
                                        _this.attr("data-value",1);
                                    }


                                $.alert({
                                    columnClass: 'medium',
                                    title: 'Success',
                                    icon: 'fa fa-check',
                                    type: 'green',
                                    content:  data.message,
                                    });
                                }else{
                                        $.alert({
                                            columnClass: 'col-md-8',
                                            title: 'Error',
                                            icon:  'fa fa-warning',
                                            type:  'red',
                                            content: data.message,
                                        });
                                }
                                },
                                error: function (xhr, ajaxOptions, thrownError) {
                                    var errors = JSON.parse(xhr.responseText);
                                    console.log(errors);
                                    var errs = '';
                                    $.each( errors.errors, function( key, value ) {
                                        console.log( key + ": " + value );
                                        errs += value;
                                        errs += "<br>";
                                    });
                                $.alert({
                                // columnClass: 'medium',
                                    title: errors.message,
                                    icon:  'fa fa-warning',
                                    type:  'red',
                                        content:  errs,
                                    });
                                }
                    });
                }
            },
            cancelAction: {
                text: 'Cancel'
            }
        }
    });
});
</script>

<style>
.card-header .card-title {
    color: #495057;
    font-weight: 600;
}

.form-label {
    font-weight: 500;
    color: #495057;
    margin-bottom: 0.5rem;
}

.form-label i {
    color: #6c757d;
    margin-right: 0.5rem;
}

.d-flex.gap-2 > * {
    margin-right: 0.5rem;
}

.d-flex.gap-2 > *:last-child {
    margin-right: 0;
}

#filterSection {
    transition: all 0.3s ease;
}

.btn-group-sm > .btn, .btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

.user-avatar {
    margin-right: 0.5rem !important;
}

.table th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
    color: #495057;
    font-size: 0.875rem;
}

.table th i {
    margin-right: 0.25rem;
    color: #6c757d;
}

.table td {
    vertical-align: middle;
    font-size: 0.875rem;
}

.badge {
    font-size: 0.75rem;
}

.btn-group .btn {
    margin-right: 0.25rem;
}

.btn-group .btn:last-child {
    margin-right: 0;
}

.pagination-info {
    font-size: 0.875rem;
}

.alert-info {
    border-left: 4px solid #17a2b8;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.025);
}

@media (max-width: 768px) {
    .btn-group {
        flex-direction: column;
    }

    .btn-group .btn {
        margin-bottom: 0.25rem;
        margin-right: 0;
    }

    .d-flex.gap-2 {
        flex-direction: column;
    }

    .d-flex.gap-2 > * {
        margin-right: 0;
        margin-bottom: 0.5rem;
    }
}
</style>
@endsection
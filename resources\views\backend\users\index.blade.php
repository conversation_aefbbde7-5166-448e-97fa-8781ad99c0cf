@extends('backend.layouts.app')
@section('title', 'Manage '.getRoleLabel($role, 'list'))
@section('content')

@php
$required = true;
@endphp

@push('styles')
    {{-- <link rel="stylesheet" href="{{ asset('backend/assets/css/users.css') }}"> --}}
@endpush

@push('scripts')
    {{-- <script src="{{ asset('js/users.js') }}"></script> --}}
@endpush

<div class="page-breadcrumb">
    <div class="row">
        <div class="col-5 align-self-center">
            <h4 class="page-title">{{ getRoleLabel($role, 'list') }}</h4>
        </div>
        <div class="col-7 align-self-center">
            <div class="d-flex align-items-center justify-content-end">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item">
                            <a href="{{ route('admin.dashboard')}}">Home</a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">{{ getRoleLabel($role, 'list') }}</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>
</div>
<!-- ============================================================== -->
<!-- Container fluid  -->
<!-- ============================================================== -->
<div class="container-fluid">
    <!-- ============================================================== -->
    <!-- Sales chart -->
    <!-- ============================================================== -->
    <div class="row">@include('backend.default_alert')</div>

    <!-- Enhanced Filter Section -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-filter"></i> Advanced Filters
                        <button type="button" class="btn btn-sm btn-outline-secondary float-right" id="toggleFilters">
                            <i class="fas fa-chevron-up"></i> Collapse
                        </button>
                    </h5>
                </div>
                <div class="card-body" id="filterSection">
                    <form action="{{ route('admin.users.index') }}" method="GET" class="w-100">
                        <div class="row">
                            <!-- Keyword Search -->
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label for="keyword" class="form-label">
                                        <i class="fas fa-search"></i> Search Keyword
                                    </label>
                                    <input type="text"
                                           name="keyword"
                                           id="keyword"
                                           class="form-control"
                                           placeholder="Name, Email, Mobile, Company..."
                                           value="{{ request('keyword') }}"
                                           title="Search by name, email, mobile number, or company name">
                                </div>
                            </div>

                            <!-- State Dropdown -->
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label for="state" class="form-label">
                                        <i class="fas fa-map-marker-alt"></i> State
                                    </label>
                                    {{ Form::select('state', ['' => 'All States'] + ($states ? $states->toArray() : []), request('state'), [
                                        'class' => 'form-control',
                                        'id' => 'filter_state_id'
                                    ]) }}
                                </div>
                            </div>

                            <!-- City Dropdown -->
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label for="city" class="form-label">
                                        <i class="fas fa-building"></i> City
                                    </label>
                                    {{ Form::select('city', ['' => 'All Cities'] + ($cities ? $cities->toArray() : []), request('city'), [
                                        'class' => 'form-control',
                                        'id' => 'filter_city_id'
                                    ]) }}
                                </div>
                            </div>

                            <!-- Status Filter -->
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label for="status" class="form-label">
                                        <i class="fas fa-toggle-on"></i> Status
                                    </label>
                                    <select name="status" id="status" class="form-control">
                                        <option value="">All Status</option>
                                        <option value="1" {{ request('status') == '1' ? 'selected' : '' }}>Active</option>
                                        <option value="0" {{ request('status') == '0' ? 'selected' : '' }}>Inactive</option>
                                    </select>
                                </div>
                            </div>

                            <!-- Date From -->
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label for="date_from" class="form-label">
                                        <i class="fas fa-calendar-alt"></i> Date From
                                    </label>
                                    <input type="date"
                                           name="date_from"
                                           id="date_from"
                                           class="form-control"
                                           value="{{ request('date_from') }}">
                                </div>
                            </div>

                            <!-- Date To -->
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label for="date_to" class="form-label">
                                        <i class="fas fa-calendar-alt"></i> Date To
                                    </label>
                                    <input type="date"
                                           name="date_to"
                                           id="date_to"
                                           class="form-control"
                                           value="{{ request('date_to') }}">
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-flex gap-2 justify-content-between">
                                        <div class="d-flex gap-2">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-search"></i> Search
                                            </button>
                                            <a href="{{ route('admin.users.index') }}" class="btn btn-secondary">
                                                <i class="fas fa-times"></i> Clear
                                            </a>
                                        </div>
                                        <div class="d-flex gap-2">
                                            <div class="btn-group">
                                                <button type="button" class="btn btn-info" id="exportBtn">
                                                    <i class="fas fa-download"></i> Export Excel
                                                </button>
                                                <button type="button" class="btn btn-info dropdown-toggle dropdown-toggle-split" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                    <span class="sr-only">Toggle Dropdown</span>
                                                </button>
                                                <div class="dropdown-menu">
                                                    <a class="dropdown-item" href="#" id="exportExcelBtn">
                                                        <i class="fas fa-file-excel text-success"></i> Export as Excel (.xlsx)
                                                    </a>
                                                    <a class="dropdown-item" href="#" id="exportCsvBtn">
                                                        <i class="fas fa-file-csv text-info"></i> Export as CSV (.csv)
                                                    </a>
                                                    <div class="dropdown-divider"></div>
                                                    <a class="dropdown-item" href="#" id="exportSelectedBtn" style="display: none;">
                                                        <i class="fas fa-check-square text-warning"></i> Export Selected Only
                                                    </a>
                                                </div>
                                            </div>
                                            <button type="button" class="btn btn-success" id="bulkActionsBtn" style="display: none;">
                                                <i class="fas fa-cogs"></i> Bulk Actions
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

   	<!-- Results Summary -->
    @if(request()->hasAny(['keyword', 'state', 'city', 'date_from', 'date_to']))
    <div class="row">
        <div class="col-12">
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i>
                <strong>Search Results:</strong> Found {{ $data->total() }} {{ $data->total() == 1 ? 'user' : 'users' }}
                @if(request('keyword'))
                    matching "<strong>{{ request('keyword') }}</strong>"
                @endif
                @if(request('state') && $states)
                    in state "<strong>{{ $states->get(request('state')) }}</strong>"
                @endif
                @if(request('city') && $cities)
                    in city "<strong>{{ $cities->get(request('city')) }}</strong>"
                @endif
                @if(request('date_from') || request('date_to'))
                    @if(request('date_from') && request('date_to'))
                        between {{ request('date_from') }} and {{ request('date_to') }}
                    @elseif(request('date_from'))
                        from {{ request('date_from') }}
                    @else
                        until {{ request('date_to') }}
                    @endif
                @endif
            </div>
        </div>
    </div>
    @endif

    <!-- Quick Stats -->
    <div class="row mb-3">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="me-3">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                        <div>
                            <h4 class="mb-0">{{ $data->total() }}</h4>
                            <small>Total Users</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="me-3">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                        <div>
                            <h4 class="mb-0">{{ $data->where('status', 1)->count() }}</h4>
                            <small>Active Users</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="me-3">
                            <i class="fas fa-times-circle fa-2x"></i>
                        </div>
                        <div>
                            <h4 class="mb-0">{{ $data->where('status', 0)->count() }}</h4>
                            <small>Inactive Users</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="me-3">
                            <i class="fas fa-key fa-2x"></i>
                        </div>
                        <div>
                            <h4 class="mb-0">{{ $data->sum('total_keys') }}</h4>
                            <small>Total Keys</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

   	<!-- Users Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="card-title mb-0">
                            <i class="fas fa-users"></i> {{ getRoleLabel($role, 'list') }} Manager
                        </h5>
                        <small class="text-muted">Total: {{ $data->total() }} {{ $data->total() == 1 ? 'user' : 'users' }}</small>
                    </div>
                    <div>
                        <a class="btn btn-success" href="{{ route('admin.users.create') }}">
                            <i class="fa fa-plus"></i> {{ getRoleLabel($role, 'add') }}
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    @if($data->count() > 0)
                        <div class="table-responsive">
                            <table id="zero_config" class="table table-striped table-bordered table-hover">
                                <thead class="thead-light">
                                    <tr>
                                        <th width="3%">
                                            <div class="custom-control custom-checkbox">
                                                <input type="checkbox" class="custom-control-input" id="selectAll">
                                                <label class="custom-control-label" for="selectAll"></label>
                                            </div>
                                        </th>
                                        <th width="4%">#</th>
                                        <th width="15%">
                                            <i class="fas fa-user"></i> Name
                                        </th>
                                        {{-- <th width="15%">
                                            <i class="fas fa-building"></i>Company Name
                                        </th> --}}
                                        {{-- <th width="15%">
                                            <i class="fas fa-envelope"></i> Email
                                        </th> --}}
                                        <th width="10%">
                                            <i class="fas fa-phone"></i> Mobile
                                        </th>
                                        <th width="15%">
                                            <i class="fas fa-map-marker-alt"></i> Location
                                        </th>
                                        <th width="10%">
                                            <i class="fas fa-tag"></i> Plan
                                        </th>
                                        <th width="8%">
                                            <i class="fas fa-key"></i> Keys
                                        </th>
                                        <th width="8%">
                                            <i class="fas fa-rupee-sign"></i> Price/Key
                                        </th>
                                        <th width="8%">
                                            <i class="fas fa-toggle-on"></i> Status
                                        </th>
                                        <th width="10%">
                                            <i class="fas fa-calendar"></i> Reg. Date
                                        </th>
                                        <th width="16%">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($data as $key => $user)
                                    <tr>
                                        <td>
                                            <div class="custom-control custom-checkbox">
                                                <input type="checkbox" class="custom-control-input user-checkbox"
                                                       id="user_{{ $user->id }}" value="{{ $user->id }}">
                                                <label class="custom-control-label" for="user_{{ $user->id }}"></label>
                                            </div>
                                        </td>
                                        <td>{{ ($data->currentPage() - 1) * $data->perPage() + $key + 1 }}</td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="user-avatar bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 35px; height: 35px; font-size: 14px;">
                                                    {{ strtoupper(substr($user->username, 0, 2)) }}
                                                </div>
                                                <div>
                                                    <strong>{{ $user->username }}</strong>
                                                    @if($user->company_name)
                                                        <br><small class="text-muted">{{ $user->company_name }}</small>
                                                    @endif
                                                </div>
                                            </div>
                                        </td>
                                        {{-- <td>
                                            <a href="mailto:{{ $user->email }}" class="text-decoration-none">
                                                {{ $user->email }}
                                            </a>
                                        </td> --}}
                                        <td>
                                            <a href="tel:{{ $user->mobile }}" class="text-decoration-none">
                                                {{ $user->mobile }}
                                            </a>
                                        </td>
                                        <td>
                                            <div>
                                                @if($user->stateDetail)
                                                    <i class="fas fa-map-marker-alt text-muted"></i> {{ $user->stateDetail->name }}
                                                @endif
                                                @if($user->cityDetail)
                                                    <br><small class="text-muted">{{ $user->cityDetail->name }}</small>
                                                @endif
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge badge-info">
                                                {{ ucwords(str_replace('_', ' ', $user->plan)) }}
                                            </span>
                                        </td>
                                        <td>
                                            <div class="text-center">
                                                <strong>{{ $user->total_keys }}</strong>
                                                <br><small class="text-muted">Used: {{ $user->used_keys ?? 0 }}</small>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="text-success font-weight-bold">
                                                ₹{{ number_format($user->price_per_key, 2) }}
                                            </span>
                                        </td>
                                        <td>
                                            <div class="text-center">
                                                @if($user->status == 1)
                                                    <button class="btn btn-sm btn-success updateStatus"
                                                            data-url="{{ route('admin.users.update-status', $user->id) }}"
                                                            data-value="1"
                                                            data-column="status"
                                                            title="Click to deactivate">
                                                        <i class="fas fa-check-circle"></i> Active
                                                    </button>
                                                @else
                                                    <button class="btn btn-sm btn-danger updateStatus"
                                                            data-url="{{ route('admin.users.update-status', $user->id) }}"
                                                            data-value="0"
                                                            data-column="status"
                                                            title="Click to activate">
                                                        <i class="fas fa-times-circle"></i> Inactive
                                                    </button>
                                                @endif
                                            </div>
                                        </td>
                                        <td>
                                            <div>
                                                {{ date("M d, Y", strtotime($user->created_at)) }}
                                                {{-- <br><small class="text-muted">{{ $user->getRoleNames()->first() }}</small> --}}
                                            </div>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a class="btn btn-sm btn-outline-primary"
                                                   href="{{ route('admin.users.show', $user->id) }}"
                                                   title="View User">
                                                    <i class="fa fa-eye"></i>
                                                </a>
                                                <a class="btn btn-sm btn-outline-secondary"
                                                   href="{{ route('admin.users.edit', $user->id) }}"
                                                   title="Edit User">
                                                    <i class="fa fa-edit"></i>
                                                </a>
                                                <button class="btn btn-sm btn-outline-warning changePassword"
                                                        data-user-id="{{ $user->id }}"
                                                        data-user-name="{{ $user->username }}"
                                                        title="Change Password">
                                                    <i class="fas fa-key"></i>
                                                </button>
                                                @if($user->total_keys > 0 || Auth::user()->total_keys > Auth::user()->used_keys)
                                                <button class="btn btn-sm btn-outline-info transferKeys"
                                                        data-user-id="{{ $user->id }}"
                                                        data-user-name="{{ $user->username }}"
                                                        data-available-keys="{{ max(($user->total_keys ?? 0) - ($user->used_keys ?? 0), 0) }}"
                                                        title="Transfer Keys">
                                                    <i class="fas fa-exchange-alt"></i>
                                                </button>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination with info -->
                        <div class="d-flex justify-content-between align-items-center mt-3">
                            <div class="pagination-info">
                                <small class="text-muted">
                                    Showing {{ $data->firstItem() ?? 0 }} to {{ $data->lastItem() ?? 0 }} of {{ $data->total() }} results
                                </small>
                            </div>
                            <div>
                                {{ $data->appends(request()->query())->links() }}
                            </div>
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No users found</h5>
                            <p class="text-muted">
                                @if(request()->hasAny(['keyword', 'state', 'city', 'date_from', 'date_to']))
                                    Try adjusting your search criteria or
                                    <a href="{{ route('admin.users.index') }}" class="text-primary">clear all filters</a>
                                @else
                                    <a href="{{ route('admin.users.create') }}" class="btn btn-primary">
                                        <i class="fa fa-plus"></i> Add your first {{ getRoleLabel($role, 'single') }}
                                    </a>
                                @endif
                            </p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Change Password Modal -->
<div class="modal fade" id="changePasswordModal" tabindex="-1" role="dialog" aria-labelledby="changePasswordModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="changePasswordModalLabel">
                    <i class="fas fa-key"></i> Change Password
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="changePasswordForm">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="userName">User</label>
                        <input type="text" class="form-control" id="userName" readonly>
                        <input type="hidden" id="userId">
                    </div>
                    <div class="form-group">
                        <label for="newPassword">New Password <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <input type="password" class="form-control" id="newPassword" required minlength="6">
                            <div class="input-group-append">
                                <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                        <small class="form-text text-muted">Password must be at least 6 characters long.</small>
                    </div>
                    <div class="form-group">
                        <label for="confirmPassword">Confirm Password <span class="text-danger">*</span></label>
                        <input type="password" class="form-control" id="confirmPassword" required minlength="6">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">
                        <i class="fas fa-times"></i> Cancel
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Update Password
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Transfer Keys Modal -->
<div class="modal fade" id="transferKeysModal" tabindex="-1" role="dialog" aria-labelledby="transferKeysModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="transferKeysModalLabel">
                    <i class="fas fa-exchange-alt"></i> Transfer Keys
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="transferKeysForm">
                <div class="modal-body">
                    <div class="row">
                        <!-- User Information -->
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">
                                        <i class="fas fa-user"></i> From User
                                    </h6>
                                    <div class="form-group">
                                        <label for="fromUserName">User Name</label>
                                        <input type="text" class="form-control" id="fromUserName" readonly>
                                        <input type="hidden" id="fromUserId">
                                    </div>
                                    <div class="form-group">
                                        <label for="availableKeys">Available Keys</label>
                                        <input type="text" class="form-control" id="availableKeys" readonly>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Transfer Details -->
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">
                                        <i class="fas fa-cogs"></i> Transfer Details
                                    </h6>
                                    <div class="form-group">
                                        <label for="transferAction">Action <span class="text-danger">*</span></label>
                                        <select name="action" id="transferAction" class="form-control" required>
                                            <option value="">Select Action</option>
                                            <option value="add">Add Keys (Credit)</option>
                                            <option value="subtract">Subtract Keys (Debit)</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label for="transferQuantity">Quantity <span class="text-danger">*</span></label>
                                        <input type="number" name="quantity" id="transferQuantity" class="form-control"
                                               placeholder="Enter number of keys" required min="1">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {{-- <div class="row">
                        <!-- Target User Dropdown -->
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="targetUser">Assigned User</label>
                                <select name="target_user" id="targetUser" class="form-control">
                                    <option value="">Select User</option>
                                    @foreach($data as $targetUser)
                                        <option value="{{ $targetUser->id }}">{{ $targetUser->username }} ({{ $targetUser->email }})</option>
                                    @endforeach
                                </select>
                                <small class="form-text text-muted">This will be used in future transfer module</small>
                            </div>
                        </div>
                    </div> --}}

                    <div class="row">
                        <div class="col-12">
                            <div class="form-group">
                                <label for="transferNote">Note</label>
                                <textarea name="note" id="transferNote" class="form-control" rows="3"
                                          placeholder="Optional note about this transfer"></textarea>
                            </div>
                        </div>
                    </div>

                    <!-- Transfer Summary -->
                    <div class="row" id="transferSummary" style="display: none;">
                        <div class="col-12">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle"></i> Transfer Summary</h6>
                                <div id="summaryContent"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">
                        <i class="fas fa-times"></i> Cancel
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-exchange-alt"></i> Transfer Keys
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Export Summary Modal -->
<div class="modal fade" id="exportSummaryModal" tabindex="-1" role="dialog" aria-labelledby="exportSummaryModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exportSummaryModalLabel">
                    <i class="fas fa-download"></i> Export Summary
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <i class="fas fa-users fa-2x text-primary mb-2"></i>
                                <h4 id="exportTotalUsers">0</h4>
                                <small>Total Users to Export</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <i class="fas fa-file-excel fa-2x text-success mb-2"></i>
                                <h4 id="exportFormat">XLSX</h4>
                                <small>Export Format</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="mt-3">
                    <h6><i class="fas fa-filter"></i> Applied Filters:</h6>
                    <div id="appliedFilters" class="text-muted">
                        <small>No filters applied</small>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                    <i class="fas fa-times"></i> Cancel
                </button>
                <button type="button" class="btn btn-success" id="confirmExportBtn">
                    <i class="fas fa-download"></i> Proceed with Export
                </button>
            </div>
        </div>
    </div>
</div>

<script>
  window.usersConfig = {
    citiesRoute: "{{ route('admin.location.cities', '') }}",
    bulkUpdateRoute: "{{ route('admin.users.bulk-update-status') }}",
    changePasswordRoute: "{{ route('admin.users.change-password') }}",
    updateKeysRoute: "{{ route('admin.users.update-keys', '') }}",
    exportRoute: "{{ route('admin.users.export') }}",
    indexRoute: "{{ route('admin.users.index') }}",
    selectedCity: "{{ request('city') }}",
    searchKeyword: "{{ request('keyword') }}",
    totalUsers: {{ $data->total() ?? 0 }}
  };
</script>

<!-- ============================================================== -->
<!-- End Container fluid  -->
<!-- ============================================================== -->
@endsection

@section('styles')
    <link rel="stylesheet" href="{{ asset('backend/assets/css/users.css') }}">
@endsection

@section('footer_js')
    <script src="{{ asset('backend/assets/js/users.js') }}"></script>
@endsection
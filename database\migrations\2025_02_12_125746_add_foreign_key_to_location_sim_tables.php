<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddForeignKeyToLocationSimTables extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('new_phone_device_sim', function (Blueprint $table) {
			$table->Integer('device_id')->after('id')->nullable();
            $table->foreign('device_id')->references('id')->on('new_phone_device_info')->onDelete('cascade');
        });
		Schema::table('new_phone_device_locations', function (Blueprint $table) {
			$table->Integer('device_id')->after('id')->nullable();
            $table->foreign('device_id')->references('id')->on('new_phone_device_info')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('new_phone_device_sim', function (Blueprint $table) {
			$table->dropColumn(['device_id']);
            $table->dropForeign(['device_id']);
        });
		
		Schema::table('new_phone_device_locations', function (Blueprint $table) {
			$table->dropColumn(['device_id']);
            $table->dropForeign(['device_id']);
        });
    }
}

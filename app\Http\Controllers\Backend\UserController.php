<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Exception;
use App\Models\User;
use Illuminate\Support\Arr;
use App\Models\Country;
use App\Models\State;
use App\Models\City;
use Illuminate\Support\Facades\Auth;
use App\Http\Requests\Backend\UpdateUserProfile;
use App\Http\Requests\Backend\AddUser;
use Spatie\Permission\Models\Role;
use App\Models\KeysLog;
use App\Exports\UsersExport;
use App\Exports\UsersCsvExport;
use Maatwebsite\Excel\Facades\Excel;

class UserController extends Controller
{
    
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $query = User::where('admin_id', Auth::id());

        // Keyword search (name, email, mobile, company name)
        if ($request->has('keyword') && !empty($request->keyword)) {
            $keyword = $request->keyword;
            $query->where(function($q) use ($keyword) {
                $q->where('username', 'LIKE', "%{$keyword}%")
                  ->orWhere('email', 'LIKE', "%{$keyword}%")
                  ->orWhere('mobile', 'LIKE', "%{$keyword}%")
                  ->orWhere('company_name', 'LIKE', "%{$keyword}%");
            });
        }

        // State filter
        if ($request->has('state') && !empty($request->state)) {
            $query->where('state', $request->state);
        }

        // City filter
        if ($request->has('city') && !empty($request->city)) {
            $query->where('city', $request->city);
        }

        // Status filter
        if ($request->has('status') && $request->status !== '') {
            $query->where('status', $request->status);
        }

        // Date range filter
        if ($request->has('date_from') && !empty($request->date_from)) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->has('date_to') && !empty($request->date_to)) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $data = $query->latest()->paginate(20);

        // Get states and cities for dropdowns
        $states = State::orderBy('name')->pluck('name', 'id');
        $cities = collect();

        if ($request->has('state') && !empty($request->state)) {
            $cities = City::where('state_id', $request->state)->orderBy('name')->pluck('name', 'id');
        }

        $user = Auth::user();
        $role = $user->getRoleNames()->first();

        return view('backend.users.index', compact('data', 'role', 'states', 'cities'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
		$countries = Country::orderBy('name')->pluck('name', 'id');
        $user = Auth::user();

        $roleMap = [
            'super_admin' => ['admin'],
            'admin' => ['national_distributor'],
            'national_distributor' => ['super_distributor'],
            'super_distributor' => ['distributor'],
            'distributor' => ['retailer'],
        ];

        $roles = collect($roleMap)->filter(function ($_, $key) use ($user) {
            return $user->hasRole($key);
        })->flatten()->toArray();

        $role = $user->getRoleNames()->first();

        return view('backend.users.create', compact('countries', 'roles', 'role'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(AddUser $request)
    {      
        $input = $request->only(['username', 'email', 'mobile', 'whatsapp_number',
                                'company_name', 'gst_number',
								 'address', 'country', 'city', 'state', 'zip', 'pincode',
            					'plan', 'total_keys', 'price_per_key', 'total_price'
        ]);

        $parent = Auth::user();

        // Check if parent user has enough available keys
		$availableKeys = $parent->total_keys - $parent->used_keys;

        if ($request->total_keys > $availableKeys) {
			return back()->withErrors(['error' => 'Not enough available keys.']);
		}

        $input['password'] = \Hash::make($request->password);
        $input['is_admin'] = 0;
        $input['admin_id'] = Auth::id();

        $user = User::create($input);

        $user->assignRole($request->role);

        // Update parent used_keys
    	$parent->increment('used_keys', $request->total_keys);

        return redirect()->route('admin.users.index')->with('alert-success', 'User created successfully.');
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $user = User::find($id);
        
        return view('backend.users.show', compact('user'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $authUser = Auth::user();
        $user = User::where(['id'=>$id, 'admin_id'=>Auth::id()])->first();
		
		if($user == null) {
            return redirect()->route('admin.users.index')->with('alert-danger', 'User not found.');
        }
        
		$countries = Country::orderBy('name')->pluck('name', 'id');
		$states = State::where(['country_id'=> $user->country])->pluck('name', 'id');
		$cities = City::where(['state_id'=> $user->state])->pluck('name', 'id');
        $role = $authUser->getRoleNames()->first();
		
		//$countries = Country::pluck('name', 'id');
    
        return view('backend.users.edit', compact('user', 'countries', 'states', 'cities', 'role'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(UpdateUserProfile $request, $id)
    {
		$input = $request->only(['username', 'email', 'mobile', 'whatsapp_number', 
            'company_name', 'gst_number',
            'address', 'country', 'city', 'state', 'zip', 'pincode',
            'plan', 'total_keys', 'price_per_key', 'total_price','status'
        ]);
		
        if(!empty($input['password'])) { 
            $input['password'] = Hash::make($input['password']);
        } else {
            $input = Arr::except($input, array('password'));    
        }
		
        $user = User::find($id);
        $user->update($input);
        
        return redirect()->route('admin.users.index')->with('alert-success', 'Record updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        User::where(['id'=>$id, 'admin_id'=>Auth::id()])->first();

        if($user == null) {
            return redirect()->route('admin.users.index')->with('alert-danger', 'User not found.');
        }
		
        return redirect()->route('admin.users.index')
            ->with('alert-success', 'User deleted successfully.');
    }

    /*
     * Update the specified resource in storage.
     * @param  Request $request
     * @return Response
     */
    public function changeStatus(Request $request, $id) {
        $user = User::where(['id'=>$id, 'admin_id'=>Auth::id()])->first();

        if($user == null) {
            return ['success' => false, 'message' => 'User not found.'];
        }

        $user->status = $request->input('status');

        if ($user->save()) {
            return [
                'success' => true,
                'message' => $user->status == 1
                    ? 'This User activated successfully.'
                    : 'This User deactivated successfully.'
            ];
        }

        return ['success' => false, 'message' => 'Your process failed. Please try again.'];
    }

    /**
     * Change user password
     * @param  Request $request
     * @return Response
     */
    public function changePassword(Request $request) {
        $request->validate([
            'user_id' => 'required|exists:users,id',
            'password' => 'required|min:6|confirmed',
        ]);

        $user = User::where(['id' => $request->user_id, 'admin_id' => Auth::id()])->first();

        if($user == null) {
            return response()->json(['success' => false, 'message' => 'User not found.'], 404);
        }

        $user->password = Hash::make($request->password);

        if ($user->save()) {
            return response()->json([
                'success' => true,
                'message' => 'Password changed successfully for ' . $user->username
            ]);
        }

        return response()->json(['success' => false, 'message' => 'Failed to change password. Please try again.'], 500);
    }

    /**
     * Bulk update user status
     * @param  Request $request
     * @return Response
     */
    public function bulkUpdateStatus(Request $request) {
        $request->validate([
            'user_ids' => 'required|array',
            'user_ids.*' => 'exists:users,id',
            'status' => 'required|in:0,1',
        ]);

        $userIds = $request->user_ids;
        $status = $request->status;

        // Only update users that belong to the current admin
        $updatedCount = User::whereIn('id', $userIds)
                           ->where('admin_id', Auth::id())
                           ->update(['status' => $status]);

        if ($updatedCount > 0) {
            $statusText = $status == 1 ? 'activated' : 'deactivated';
            return response()->json([
                'success' => true,
                'message' => "{$updatedCount} users {$statusText} successfully."
            ]);
        }

        return response()->json(['success' => false, 'message' => 'No users were updated.'], 400);
    }

    /**
     * Export users to Excel/CSV
     * @param  Request $request
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function export(Request $request)
    {
        $filters = $request->only(['keyword', 'state', 'city', 'status', 'date_from', 'date_to']);
        $format = $request->get('format', 'xlsx');
        $selectedIds = $request->get('selected_ids', []);

        // Add selected IDs to filters if provided
        if (!empty($selectedIds)) {
            $filters['selected_ids'] = $selectedIds;
        }

        $extension = $format === 'csv' ? 'csv' : 'xlsx';
        $filename = 'users_export_' . date('Y-m-d_H-i-s') . '.' . $extension;

        // Use appropriate export class based on format
        $exportClass = $format === 'csv' ? new UsersCsvExport($filters) : new UsersExport($filters);

        return Excel::download($exportClass, $filename);
    }

    public function manageKeys($id)
    {
        $user = User::where(['id'=>$id, 'admin_id'=>auth()->user()->id])->first();
        
        return view('backend.retailers.manage_keys', compact('user'));
    }
	
	public function showKeys($id)
    {
        $logs = KeysLog::where(['assigned_to' => $id])->paginate();
        
        return view('backend.retailers.show_keys_log', compact('logs'));
    }
	
	public function updateKeys(Request $request, $id)
	{
		$request->validate([
			'action' => 'required|in:add,subtract',
			'quantity' => 'required|integer|min:1',
			'note' => 'nullable|string'
		]);
		
		try {

			$admin = auth()->user();

			$assignedUser = User::where(['id'=>$id, 'admin_id'=>auth()->user()->id])->first();

			$quantity = $request->quantity;

			if ($request->action == 'add') {
				$availableKeys = $admin->total_keys - $admin->used_keys;
				
				if ($quantity > $availableKeys) {
					return back()->withErrors(['error' => 'Not enough available keys.']);
				}

				$assignedUser->increment('total_keys', $quantity);
				$admin->increment('used_keys', $quantity);
				
			} else {
				
				$remainingKeys = $assignedUser->total_keys - $assignedUser->used_keys;
				
				if ($quantity > $remainingKeys) {
					return back()->withErrors(['error' => 'User does not have enough keys to subtract.']);
				}

				$assignedUser->decrement('total_keys', $quantity);
				$admin->decrement('used_keys', $quantity); // Optional: if you want keys to be returned
			}
			

			KeysLog::create([
				'assigned_to' => $assignedUser->id,
				'changed_by' => auth()->id(), // current admin
				'action' => $request->action,
				'quantity' => $quantity,
				'total_after_change' => $assignedUser->total_keys,
				'note' => $request->note
			]);

			return redirect()->route('admin.users.index')->with('alert-success', 'Keys Updated successfully.');

		} catch(Exception $e) {

			return redirect()->route('admin.users.index')->with('alert-danger', $e->getMessage());
		}
	}
}

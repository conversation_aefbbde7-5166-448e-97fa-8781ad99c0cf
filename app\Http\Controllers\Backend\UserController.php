<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Exception;
use App\Models\User;
use Illuminate\Support\Arr;
use App\Models\Country;
use App\Models\State;
use App\Models\City;
use Illuminate\Support\Facades\Auth;
use App\Http\Requests\Backend\UpdateUserProfile;
use App\Http\Requests\Backend\AddUser;
use Spatie\Permission\Models\Role;
use App\Models\KeysLog;

class UserController extends Controller
{
    
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        //$data = User::where(['is_admin'=>0, 'role'=>'admin'])->orderBy('id', 'desc')->paginate(20);
        $data = User::where('admin_id', Auth::id())->latest()->paginate(20);

        $user = Auth::user();
        $role = $user->getRoleNames()->first();
		
        return view('backend.users.index', compact('data', 'role'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
		$countries = Country::orderBy('name')->pluck('name', 'id');
        $user = Auth::user();

        $roleMap = [
            'super_admin' => ['admin'],
            'admin' => ['national_distributor'],
            'national_distributor' => ['super_distributor'],
            'super_distributor' => ['distributor'],
            'distributor' => ['retailer'],
        ];

        $roles = collect($roleMap)->filter(function ($_, $key) use ($user) {
            return $user->hasRole($key);
        })->flatten()->toArray();

        $role = $user->getRoleNames()->first();

        return view('backend.users.create', compact('countries', 'roles', 'role'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(AddUser $request)
    {      
        $input = $request->only(['username', 'email', 'mobile', 'whatsapp_number',
                                'company_name', 'gst_number',
								 'address', 'country', 'city', 'state', 'zip', 'pincode',
            					'plan', 'total_keys', 'price_per_key', 'total_price'
        ]);

        $parent = Auth::user();

        // Check if parent user has enough available keys
		$availableKeys = $parent->total_keys - $parent->used_keys;

        if ($request->total_keys > $availableKeys) {
			return back()->withErrors(['error' => 'Not enough available keys.']);
		}

        $input['password'] = \Hash::make($request->password);
        $input['is_admin'] = 0;
        $input['admin_id'] = Auth::id();

        $user = User::create($input);

        $user->assignRole($request->role);

        // Update parent used_keys
    	$parent->increment('used_keys', $request->total_keys);

        return redirect()->route('admin.users.index')->with('alert-success', 'User created successfully.');
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $user = User::find($id);
        
        return view('backend.users.show', compact('user'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $authUser = Auth::user();
        $user = User::where(['id'=>$id, 'admin_id'=>Auth::id()])->first();
		
		if($user == null) {
            return redirect()->route('admin.users.index')->with('alert-danger', 'User not found.');
        }
        
		$countries = Country::orderBy('name')->pluck('name', 'id');
		$states = State::where(['country_id'=> $user->country])->pluck('name', 'id');
		$cities = City::where(['state_id'=> $user->state])->pluck('name', 'id');
        $role = $authUser->getRoleNames()->first();
		
		//$countries = Country::pluck('name', 'id');
    
        return view('backend.users.edit', compact('user', 'countries', 'states', 'cities', 'role'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(UpdateUserProfile $request, $id)
    {
		$input = $request->only(['username', 'email', 'mobile', 'whatsapp_number', 
            'company_name', 'gst_number',
            'address', 'country', 'city', 'state', 'zip', 'pincode',
            'plan', 'total_keys', 'price_per_key', 'total_price','status'
        ]);
		
        if(!empty($input['password'])) { 
            $input['password'] = Hash::make($input['password']);
        } else {
            $input = Arr::except($input, array('password'));    
        }
		
        $user = User::find($id);
        $user->update($input);
        
        return redirect()->route('admin.users.index')->with('alert-success', 'Record updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        User::where(['id'=>$id, 'admin_id'=>Auth::id()])->first();

        if($user == null) {
            return redirect()->route('admin.users.index')->with('alert-danger', 'User not found.');
        }
		
        return redirect()->route('admin.users.index')
            ->with('alert-success', 'User deleted successfully.');
    }

    /*
     * Update the specified resource in storage.
     * @param  Request $request
     * @return Response
     */
    public function changeStatus(Request $request, $id) {
        $user->status = $request->input('status');

        if ($user->save()) {
            return [
                'success' => true,
                'message' => $user->status == 1
                    ? 'This User activated successfully.'
                    : 'This User deactivated successfully.'
            ];
        }

        return ['success' => false, 'message' => 'Your process failed. Please try again.'];
    }

    public function manageKeys($id)
    {
        $user = User::where(['id'=>$id, 'admin_id'=>auth()->user()->id])->first();
        
        return view('backend.retailers.manage_keys', compact('user'));
    }
	
	public function showKeys($id)
    {
        $logs = KeysLog::where(['retailer_id'=>$id])->paginate();
        
        return view('backend.retailers.show_keys_log', compact('logs'));
    }
	
	public function updateKeys(Request $request, $id)
	{
		$request->validate([
			'action' => 'required|in:add,subtract',
			'quantity' => 'required|integer|min:1',
			'note' => 'nullable|string'
		]);
		
		try {

			$admin = auth()->user();
			$retailer = User::where(['id'=>$id, 'admin_id'=>auth()->user()->id])->first();

			$quantity = $request->quantity;

			if ($request->action == 'add') {
				$availableKeys = $admin->total_keys - $admin->used_keys;
				
				if ($quantity > $availableKeys) {
					return back()->withErrors(['error' => 'Not enough available keys.']);
				}

				$retailer->increment('total_keys', $quantity);
				$admin->increment('used_keys', $quantity);
				
			} else {
				
				$remainingKeys = $retailer->total_keys - $retailer->used_keys;
				
				if ($quantity > $remainingKeys) {
					return back()->withErrors(['error' => 'Retailer does not have enough keys to subtract.']);
				}

				$retailer->decrement('total_keys', $quantity);
				$admin->decrement('used_keys', $quantity); // Optional: if you want keys to be returned
			}
			

			KeysLog::create([
				'retailer_id' => $retailer->id,
				'changed_by' => auth()->id(), // current admin
				'action' => $request->action,
				'quantity' => $quantity,
				'total_after_change' => $retailer->total_keys,
				'note' => $request->note
			]);

			return redirect()->route('admin.retailers.index')->with('alert-success', 'Keys Updated successfully.');
		} catch(Exception $e) {
			return redirect()->route('admin.retailers.index')->with('alert-danger', $e->getMessage());
		}
	}
}

<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use DB;
use Hash;
use App\Models\User;
use Illuminate\Support\Arr;
use App\Models\Country;
use App\Models\State;
use App\Models\City;
use Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\File;
use App\Http\Requests\Backend\AgentRequest;

class AgentController extends Controller
{
    
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        //$data = User::where(['is_admin'=>0, 'role'=>'agent'])->orderBy('id', 'desc')->paginate(20);
		$data = User::role('agent')->orderBy('id', 'desc')->paginate(20);
		
        return view('backend.agents.index', compact('data'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $countries = Country::orderBy('name')->pluck('name', 'id');
        return view('backend.agents.create', compact('countries'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(AgentRequest $request)
    {      
        $input = $request->only(['username', 'email', 'mobile',
								 'address', 'country', 'city', 'state', 'zip', 'pincode',
        ]);

        $input['password'] = \Hash::make($request->password);
        //$input['is_admin'] = 0;
        //$input['role'] = 'agent';

        $user = User::create($input);
		
		$user->assignRole('agent');
        
        return redirect()->route('admin.agents.index')->with('alert-success', 'Agent created successfully.');
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $user = User::find($id);
        
        return view('backend.agents.show', compact('user'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $user = User::find($id);
        
		$countries = Country::orderBy('name')->pluck('name', 'id');
		$states = State::where(['country_id'=> $user->country])->pluck('name', 'id');
		$cities = City::where(['state_id'=> $user->state])->pluck('name', 'id');
    
        return view('backend.agents.edit', compact('user', 'countries', 'states', 'cities'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(AgentRequest $request, $id)
    {
		$input = $request->only([
			'username', 'email', 'mobile', 'address', 'country', 'city', 'state', 'zip', 'pincode',
			'password'
        ]);
		
        if(!empty($input['password'])) { 
            $input['password'] = Hash::make($input['password']);
        } else {
            $input = Arr::except($input, array('password'));    
        }
		
        $user = User::find($id);
        $user->update($input);
        
        return redirect()->route('admin.agents.index')->with('alert-success', 'Agent updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        User::find($id)->delete();
		
        return redirect()->route('admin.agents.index')
            ->with('alert-success', 'Agent deleted successfully.');
    }

    /*
     * Update the specified resource in storage.
     * @param  Request $request
     * @return Response
     */
    public function changeStatus(Request $request, $id) {
        $user = User::where('id', '=', $id)->first();
        
		if(empty($user)){
            abort(404);
        }
		
        $user->status = $request->input('status');
		
        if($user->save()){
            return ['success' => true, 'message' => $request->input('status') == 1 ?'This Agent activated successfully.' : "This Agent deactivated successfully."];
        }else{
            return ['success' => false, 'message' => 'Your process failed. please try again!!'];
        }
    }
}

<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;

class RoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $rolesWithLevels = [
            'super_admin'         => 100,
            'admin'               => 90,
            'national_distributor'=> 70,
            'super_distributor'   => 60,
            'distributor'         => 50,
            'agent'               => 20,
            'retailer'            => 10,
        ];

        foreach ($rolesWithLevels as $name => $level) {
            Role::updateOrCreate(
                ['name' => $name],
                ['level' => $level]
            );
        }
    }
}

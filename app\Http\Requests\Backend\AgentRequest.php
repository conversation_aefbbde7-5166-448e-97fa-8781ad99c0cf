<?php

namespace App\Http\Requests\Backend;


use Illuminate\Foundation\Http\FormRequest;
use \Illuminate\Http\Request;

class AgentRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(Request $request)
    {
        $rule = 
        [
            'username' => 'required',
            'address' => 'nullable|string',
            'country' => 'required|string',
            'state' => 'required|string',
            'city' => 'required|string',
            'pincode' => 'required|string',            
        ];
        if($request->segment(3)){
            $rule['email'] = 'required|email|regex:/^([a-z0-9\+_\-]+)(\.[a-z0-9\+_\-]+)*@([a-z0-9\-]+\.)+[a-z]{2,6}$/ix|unique:users,email,' . $request->segment(3);
			$rule['mobile'] = 'required|string|max:15|unique:users,mobile,' . $request->segment(3);
           
        }
        else{
            $rule['email'] = 'required|email|unique:users,email|regex:/^([a-z0-9\+_\-]+)(\.[a-z0-9\+_\-]+)*@([a-z0-9\-]+\.)+[a-z]{2,6}$/ix';
			$rule['mobile'] = 'required|string|max:15|unique:users,mobile';
			$rule['password'] = 'required|min:6';
        }
        return $rule;
    }
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'username.required' => 'Please enter your name',
            'email.required' => 'Please enter your email address',
        ];
    }
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCustomersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('customers', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('retailer_id')->index(); // Foreign key to retailer

            // Personal Details
            $table->string('name', 100);
            $table->string('email', 100)->nullable()->unique();
            $table->string('mobile', 15)->unique();
            $table->string('customer_photo', 150)->nullable();
            $table->string('address', 255)->nullable();
            $table->string('id_proof', 150)->nullable();

            // Product & Device Info
            $table->string('product', 100)->nullable();
            $table->enum('device_type', ['phone', 'tab'])->nullable();
            $table->string('imei_1', 20)->nullable();
            $table->string('imei_2', 20)->nullable();

            // Signatures
            $table->string('customer_sign', 150)->nullable();
            $table->string('retailer_sign', 150)->nullable();

            // Finance & EMI Details
            $table->string('financer_name', 100)->nullable();
            $table->decimal('product_price', 10, 2)->nullable();
            $table->decimal('downpayment', 10, 2)->nullable();
            $table->decimal('balance_amount', 10, 2)->nullable();
            $table->enum('emi_duration', ['monthly', '15 days', 'weekly'])->nullable();
            $table->tinyInteger('num_of_months_weeks')->unsigned()->nullable();
            $table->decimal('interest_rate', 5, 2)->nullable();
            $table->decimal('total_emi', 10, 2)->nullable();
            $table->decimal('monthly_weekly_emi', 10, 2)->nullable();

            // Location & Registration
            $table->string('country', 50)->default('India');
            $table->string('state', 50);
            $table->string('city', 50);
            $table->date('reg_date')->nullable();

            $table->timestamps();

            // Foreign Key Constraint
            $table->foreign('retailer_id')->references('id')->on('users')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('customers');
    }
}

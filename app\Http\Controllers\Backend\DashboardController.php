<?php

namespace App\Http\Controllers\Backend;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Auth;
use App\Models\User;
use App\Models\ApiKey;
use App\Models\Customer;
use App\Models\DeviceInfo;

class DashboardController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function index()
    {
		$user = Auth::user();

        // Step 1: Get all downline user IDs recursively
        $user->load('downlines'); // eager load to reduce queries
        $downlineIds = getAllDescendantUserIds($user);

        // Step 2: Get count of users by role
        $roleStats = User::whereIn('id', $downlineIds)
            ->with('roles')
            ->get()
            ->groupBy(function($user) {
                return $user->getRoleNames()->first(); // assuming 1 role per user
            })
            ->map(function($group) {
                return $group->count();
            });

        $retailerIds = User::whereIn('id', $downlineIds)
            ->role('Retailer')
            ->pluck('id');

        $customerStats = Customer::selectRaw("
                COUNT(*) as total_installed_imei,
                SUM(CASE WHEN deleted_at IS NOT NULL THEN 1 ELSE 0 END) as deleted_imei,
                SUM(CASE WHEN deleted_at IS NULL AND DATE(created_at) = CURDATE() THEN 1 ELSE 0 END) as today_installed_imei,
                SUM(CASE WHEN DATE(deleted_at) = CURDATE() THEN 1 ELSE 0 END) as today_deleted_imei
            ")
            ->withTrashed()
            ->first();
		
		$deviceInfo = DeviceInfo::selectRaw("
			COUNT(*) as total_installed_imei,
			SUM(CASE WHEN deleted_at IS NOT NULL THEN 1 ELSE 0 END) as deleted_imei,
			SUM(CASE WHEN deleted_at IS NULL And DATE(created_at) = CURDATE() THEN 1 ELSE 0 END) as today_installed_imei,
			SUM(CASE WHEN DATE(deleted_at) = CURDATE() THEN 1 ELSE 0 END) as today_deleted_imei
		")->withTrashed()->first();

		$countData = [
			'users' => ApIkey::count(), // Single query for users count
			'total_installed_imei' => $deviceInfo->total_installed_imei,
			'total_deleted_imei' => $deviceInfo->deleted_imei,
			'today_installed_imei' => $deviceInfo->today_installed_imei,
			'today_deleted_imei' => $deviceInfo->today_deleted_imei,
		];

        $adminStats = User::role('admin')
            ->selectRaw('SUM(total_keys) as total_keys, SUM(used_keys) as used_keys')
            ->first();

        $keyStats = [
            'total_keys' => $adminStats->total_keys,
            'used_keys' => $adminStats->used_keys,
            'remaining_keys' => max($adminStats->total_keys - $adminStats->used_keys, 0),
        ];
		
        //return view('backend/dashboards/dashboard',compact('countData'));
        return view('backend/admin-dashboard/index',compact('countData', 'roleStats', 'keyStats'));
    }

    /**
     * Show the admin profile.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function profile()
    {
        $user = User::where('id',Auth::user()->id)->first();
        return view('backend/dashboards/profile',compact('user'));
    }
    /**
     * Update the specified resource in storage.
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function updateProfile(Request $request)
    {
        try 
        {  
          $input = $request->except(['username','email']);
			
          $user = Auth::user();
          
          /*Save image */
          if($request->file('image')){
              if(\File::exists(url('uploads/users/'.$user->image))){
                  \File::delete(url('uploads/users/'.$user->image));
              }
              $image = 'users_'.time().'.'.$request->image->extension(); 
              $destinationPath = 'uploads/users';
              $request->image->move($destinationPath,$image);         
              $input['image'] = $image;

          }
		  //dd($input);
          $user->fill($input);
          $user->save();
			
          return back()->with('alert-success','Profile updated successfully');
        } catch (\Illuminate\Database\QueryException $e) {
           return back()->with('alert-danger',$e->getMessage())->withInput();
        }
    }

    /**
     * Show the admin change password.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function getPassword()
    {
		//dd(\Hash::make('867&qc%7np:>'));
        $user = User::where('id',Auth::user()->id)->with(['userProfile'])->first();
        return view('backend/dashboards/password',compact('user'));
    }
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateWhatsappChatTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('whatsapp_chats', function (Blueprint $table) {
            $table->id();
			$table->unsignedBigInteger('licenses_id');
            $table->foreign('licenses_id')->references('id')->on('user_licenses')->onDelete('cascade');
			$table->string('title',255)->nullable();
			$table->string('phone',50)->nullable();
			$table->enum('msg_type',['incoming','outgoing'])->default('incoming');
			$table->date('msg_date')->nullable();
            $table->time('msg_time')->nullable();
			$table->boolean('group')->default(1);
			$table->string('group_title',255)->nullable();
			$table->boolean('status')->default(1);
			$table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('whatsapp_chats');
    }
}

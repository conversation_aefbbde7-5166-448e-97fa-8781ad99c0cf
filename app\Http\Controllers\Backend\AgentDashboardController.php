<?php

namespace App\Http\Controllers\Backend;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;
use App\Models\User;
use Illuminate\Support\Facades\Storage;

class AgentDashboardController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function index()
    {
		
    }

    /**
     * Show the admin profile.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function profile()
    {
        $user = User::where('id',Auth::user()->id)->first();
        return view('backend/dashboards/profile',compact('user'));
    }
    /**
     * Update the specified resource in storage.
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function updateProfile(Request $request)
    {
        try 
        {  
          $input = $request->except(['username','email']);
			
          $user = Auth::user();
          
          /*Save image */
          if($request->file('image')){
              if(\File::exists(url('uploads/users/'.$user->image))){
                  \File::delete(url('uploads/users/'.$user->image));
              }
              $image = 'users_'.time().'.'.$request->image->extension(); 
              $destinationPath = 'uploads/users';
              $request->image->move($destinationPath,$image);         
              $input['image'] = $image;

          }
		  //dd($input);
          $user->fill($input);
          $user->save();
			
          return back()->with('alert-success','Profile updated successfully');
        } catch (\Illuminate\Database\QueryException $e) {
           return back()->with('alert-danger',$e->getMessage())->withInput();
        }
    }
	
	public function showChangePasswordForm()
    {
        return view('backend.agent-dashboard.change-password');
    }
    public function updatePassword(Request $request)
    {
        try {
			$request->validate([
				'current_password' => 'required',
				'new_password' => 'required|min:6|confirmed',
			]);

			$admin = Auth::user();

			if (!Hash::check($request->current_password, $admin->password)) {
				return back()->withErrors(['current_password' => 'Current password is incorrect']);
			}

			$admin->password = Hash::make($request->new_password);
			$admin->save();

			return back()->with('success', 'Password changed successfully!');
		} catch(Exception $e) {
			dd($e);
		}
    }
    
}

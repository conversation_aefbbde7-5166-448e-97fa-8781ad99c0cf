<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\AppCategory;
use App\Http\Requests\Backend\DomainWhitelist;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Validator;

class AppCategoryController extends Controller
{
    
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $data = AppCategory::orderBy('id', 'desc')->paginate(20);        
        return view('backend.app-categories.index', compact('data'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        return view('backend.app-categories.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
			'name' => [
				'required',
				'unique:app_categories,name'
			],
		]);

		if ($validator->fails()) {
			return back()->withErrors($validator)->withInput();
		}

        $input = $request->only(['name']);
        
        AppCategory::create($input);
        
        return redirect()->route('admin.app-category')->with('alert-success', 'Record has been created successfully.');
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $user = AppCategory::find($id);
        return view('backend.app-categories.edit', compact('user'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
		$validator = Validator::make($request->all(), [
			'name' => [
				'required',				
			],
		]);

		if ($validator->fails()) {
			return back()->withErrors($validator)->withInput();
		}
		
        $user = AppCategory::find($id);
		
        $input = $request->only(['name']);
		
        $user->update($input);
        
        return redirect()->route('admin.app-category')->with('alert-success', 'Record updated successfully.');
    }

    
}

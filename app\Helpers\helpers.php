<?php

if (!function_exists('getAllDescendantUserIds')) {
	function getAllDescendantUserIds($user)
	{
		$allIds = [];

		foreach ($user->downlines as $downline) {
			$allIds[] = $downline->id;
			$allIds = array_merge($allIds, getAllDescendantUserIds($downline));
		}

		return $allIds;
	}
}

if (!function_exists('getRoleLabel')) {
	function getRoleLabel($role, $action)
	{
		$key = "backend.$role.$action";
    return \Illuminate\Support\Facades\Lang::has($key)
        ? __($key)
        : ucfirst($action) . ' ' . ucfirst(str_replace('_', ' ', $role));
	}
}

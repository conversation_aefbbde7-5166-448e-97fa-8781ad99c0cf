<?php

namespace App\Http\Middleware;

use Closure;
use App\Http\Traits\ApiGlobalFunctions;
use DB;
use App\Models\User;
class CheckParentAuthorization {

    use ApiGlobalFunctions;

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next) 
    {      
        $authInformation = apache_request_headers();
        if (isset($authInformation['Authorization']) && !empty($authInformation['Authorization'])) 
        {
            $token = str_replace("Bearer", " ", $authInformation['Authorization']);
            $token = trim($token);
            $user = User::with(['userProfile'])->where('api_token', $token)->where('is_admin', 0)->first();
            if (empty($user)) {
                return $this->sendErrorForAuth($this->messageDefault('You are not authorize to access any more.'),'','200');
            } else {
                if ($user->status == 0) {
                    return $this->sendErrorForAuth($this->messageDefault('You account has been deactivated,Please contact the admin.'),'', '200');
                } else {
                    $request->attributes->set('ParentAuth', $user);
                    return $next($request);
                }
            }
        } else {           
            return $this->sendErrorForAuth($this->messageDefault('You are not authorize to access.'),'', '200');
        }
    }

}


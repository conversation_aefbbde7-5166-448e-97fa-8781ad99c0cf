<?php

namespace App\Http\Controllers\Api\v1;

use Illuminate\Support\Facades\Hash;
use App\Models\UserLicense;
use App\Models\UserPhoneDetails;
use App\Models\Sms;
use App\Models\UserCalls;
use App\Models\ContactBook;
use App\Models\Application;
use App\Models\Location;
use App\Models\Contact;
use App\Models\UserImage;
use Illuminate\Http\Request;
use App\Http\Requests\Api\PasswordRequest;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use App\Http\Resources\Collections\UserCollection;
use Illuminate\Support\Str;
use App\Http\Traits\ApiGlobalFunctions;
use DB;
use Validator;
use App\Http\Mail\VerifyUserMail;
use App\Http\Mail\NewPasswordMail;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class UsersController extends Controller {

    use ApiGlobalFunctions;   

    /**
     * Login api
     *
     * @return \Illuminate\Http\Response
     */
    public function login(Request $request) 
    {
        $input = $request->all();
        $data = [];
        try {
            $validator = Validator::make($input, ['license_key' => 'required', 'device_type' => 'required']);
            if ($validator->fails()) 
            {
                return $this->sendError('Validation Error.', $validator->errors()->first(), '200');
            } else {
                $query = UserLicense::query();
                $userLicenseExists =  $query->where('licenses_key', request('license_key'))->count();
                /*Check Authorise*/
                $userLicense =  $query->where('licenses_key', request('license_key'))->first();
                if ($userLicenseExists == 0) 
                {
                    return $this->sendError($this->messageDefault('invalid_login'), '', '200');

                } elseif ($userLicense) 
                {
                    $request->attributes->set('Auth', $userLicense);
                    if ($userLicense->is_used == 1/* && $userLicense->device_id != $input['device_id']*/) 
                    {
                        return $this->sendError('This License key is already is in used other device. Please try with new key', '', '200');
                    } else {
                        $input = $request->all();
                        $data = $userLicense;
                        $data['device_id'] = isset($input['device_id']) ? $input['device_id'] : $data['device_id'];
                        $data['device_type'] = isset($input['device_type']) ? $input['device_type'] : $data['device_type'];
                        $data['api_token'] = $userLicense->createToken('smartmovz')->accessToken;
                        
                        $currentDate = date('Y-m-d');
                        $expireDate =  date('Y-m-d', strtotime($currentDate. ' + '.$userLicense->validity_days.' days'));

                        UserLicense::where('id', $userLicense->id)
                                    ->update(['api_token' => $data['api_token'], 'device_id' => $data['device_id'], 'device_type' => $data['device_type'],'is_used' => 1 ,'activate_date'=>$currentDate , 'expiry_date'=>$expireDate]);
                        $data1['license_id'] = $userLicense->id;
                        $data1['licenses_key'] = $userLicense->licenses_key;
                        $data1['api_token']=$data['api_token'];
                       /* return $this->sendResponse($data['api_token'], $this->messageDefault('login_success'));*/
                        
                         return $this->sendResponse($data1, $this->messageDefault('login_success'));
                    }
                } else {
                    return $this->sendError($this->messageDefault('invalid_login'), '', '200');
                }
            }
        } catch (\Exception $e) {
            return $this->sendError($this->messageDefault('oops'), '', '200');
        }
    }

     /**
     * logout method 
     * 
     *
     * @param array() $user_id,$request_data(optional)
     *
     * @return array() $responseArray
     */
    public function logout(Request $request) 
    {
        $user = $request->get('Auth');
        $input = $request->all();
        $result = User::where('id', $user->id)->update(['api_token' => '', 'device_type' => '', 'device_id' => '']);
        if ($result) {
            $data = (object) [];
            return $this->sendResponse($data, $this->messageDefault('logout_success'));
        } else {
            return $this->sendError($this->messageDefault('process_failed'));
        }
    }

    /**
     * Store Phone Details
     * 
     *
     * @param array() $user_id,$request_data()
     *
     * @return array() $responseArray
     */
    public function phoneDetailStore(Request $request) 
    {
         
        $input = $request->all();
        $user = $request->get('Auth');
        $validator = Validator::make($request->all(), 
                    [
                    'imei_number' => 'required',
                    /*'model' => 'required',
                    'version' => 'required',
                    'os_name' => 'required',*/
                        ], 
                    [
                    'imei_number.required' => 'Please provide imei number.',
                    'model.required' => 'Please provide your model.',
                    'version.required' => 'Please provide your version.',
                    'os_name.required' => 'Please provide your os name.',
        ]);
        if ($validator->fails()) {
            return $this->sendError('Validation Error.', $validator->errors()->first(), '200');
        }
        try 
        {
            $phoneExist = UserPhoneDetails::where('licenses_id', $user->id)->count();
            if($phoneExist > 0)
            {
                return $this->sendError($this->messageDefault('Phone details already saved.Please try new.'), '', '200');
            }    
            $phone = new UserPhoneDetails();
            $phone->imei_number = $input['imei_number'];
            $phone->model = $input['model'] ?? NULL;
            $phone->version = $input['version'] ?? NULL;
            $phone->os_name = $input['os_name'] ?? NULL;
            $phone->user_id = $user->user_id;
            $phone->licenses_id = $user->id;
            $phone->save();
            if ($phone) {
                $data = [];
                return $this->sendResponse($data, $this->messageDefault('Phone details has been saved successfully.'));
            } else {
                return $this->sendError($this->messageDefault('Phone details Can\'t save'), '', '200');
            }      
        } catch (\Exception $e) {
            return $this->sendError($this->messageDefault('oops'));
        }
    }

    /**
     * Store Phone SMS
     * 
     *
     * @param array() $user_id,$request_data()
     *
     * @return array() $responseArray
     */
    public function phoneSMSStore(Request $request) 
    {
         
        $input = $request->all();
        $user = $request->get('Auth');
        $validator = Validator::make($request->all(), 
                    [
                    'call_type' => 'required',
                    'mobile_no' => 'required',
                    'date' => 'required',
                    'time' => 'required',
                    'sms_content' => 'required',
                        ], 
                    [
                    'call_type.required' => 'Please provide call type.',
                    'mobile_no.required' => 'Please provide your mobile no.',
                    'sms_content.required' => 'Please provide sms content.',
                    'date.required' => 'Please provide date.',
                    'time.required' => 'Please provide time.',
        ]);
        if ($validator->fails()) 
        {
            return $this->sendError('Validation Error.', $validator->errors()->first(), '200');
        }
        try 
        {
            $phone = new Sms();
            $phone->call_type = $input['call_type'];
            $phone->mobile_no = $input['mobile_no'];
            $phone->sms_content = $input['sms_content'];
            $phone->time = $input['time'];
            $phone->date = date("Y-m-d" ,  strtotime($input['time']));
            $phone->user_id = $user->user_id;
            $phone->licenses_id = $user->id;
            $phone->save();
            if ($phone) {
                $data = [];
                return $this->sendResponse($data, $this->messageDefault('Sms has been saved successfully.'));
            } else {
                return $this->sendError($this->messageDefault('Sms details Can not save'), '', '200');
            }      
        } catch (\Exception $e) {
            return $this->sendError($this->messageDefault('oops'));
        }
    }

    /**
     * Store Phone Call
     * 
     *
     * @param array() $user_id,$request_data()
     *
     * @return array() $responseArray
     */
    public function phoneCallStore(Request $request) 
    {
         
        $input = $request->all();
        $user = $request->get('Auth');
        $validator = Validator::make($request->all(), 
                    [
                    'call_type' => 'required',
                    'mobile_no' => 'required',
                    'date' => 'required',
                    'time' => 'required',
					'media' => 'max:10240',
                        ], 
                    [
                    'call_type.required' => 'Please provide call type.',
                    'mobile_no.required' => 'Please provide your mobile no.',
                    'date.required' => 'Please provide date.',
                    'time.required' => 'Please provide time.',
        ]);
        if ($validator->fails()) 
        {
            return $this->sendError('Validation Error.', $validator->errors()->first(), '200');
        }
        try 
        {
            $calls = new UserCalls();
            $calls->call_type = $input['call_type'];
            $calls->mobile_no = $input['mobile_no'];
            $calls->time = $input['time'];
            $calls->date = date("Y-m-d" ,  strtotime($input['time']));
			$calls->duration = $input['duration'] ?? 0;
            $calls->user_id = $user->user_id;
            $calls->licenses_id = $user->id;
			if (isset($_FILES['media']['name']) && $_FILES['media']['name'] != '') {
                $media = 'calls_'.time() . '.' . request()->media->getClientOriginalExtension();
                request()->media->move(public_path('uploads/user_calls/'), $media);
                $calls['media'] = $media;
            }
            $calls->save();
            if ($calls) {
                $data = (object)[];
                return $this->sendResponse($data, $this->messageDefault('User Calls has been saved successfully.'));
            } else {
                return $this->sendError($this->messageDefault('User Calls details Can not save'), '', '200');
            }      
        } catch (\Exception $e) { 
            return $this->sendError($this->messageDefault('oops'));
        }
    }

    /**
     * Store Contacts Books
     * 
     *
     * @param array() $user_id,$request_data()
     *
     * @return array() $responseArray
     */
    public function contactBook(Request $request) 
    {
         
        $input = $request->all();
        $user = $request->get('Auth');
        $validator = Validator::make($request->all(), 
                    [
                    'mobile_no' => 'required',
                    'name' => 'required',
                        ], 
                    [
                    'mobile_no.required' => 'Please provide your mobile no.',
                    'name.required' => 'Please provide name.',
        ]);
        if ($validator->fails()) 
        {
            return $this->sendError('Validation Error.', $validator->errors()->first(), '200');
        }
        try 
        {
            $calls = new ContactBook();
            $calls->name = $input['name'];
            $calls->mobile_no = $input['mobile_no'];
            $calls->user_id = $user->user_id;
            $calls->licenses_id = $user->id;
            if (isset($_FILES['photo']['name']) && $_FILES['photo']['name'] != '') {
                $profile_photo = 'contact_'.time() . '.' . request()->photo->getClientOriginalExtension();
                request()->photo->move(public_path('uploads/contact/'), $profile_photo);
                $calls['photo'] = $profile_photo;
            }
            $calls->save();
            if ($calls) {
                $data = (object)[];
                return $this->sendResponse($data, $this->messageDefault('Contact book has been saved successfully.'));
            } else {
                return $this->sendError($this->messageDefault('Contact book details Can not save'), '', '200');
            }      
        } catch (\Exception $e) { 
            return $this->sendError($this->messageDefault('oops'));
        }
    }

    /**
     * Store store Application
     * 
     *
     * @param array() $user_id,$request_data()
     *
     * @return array() $responseArray
     */
    public function storeApplication(Request $request) 
    {
        $input = $request->all();
        $user = $request->get('Auth');
        $validator = Validator::make($request->all(), 
                    [
                    //'package_name' => 'required',
                    //'name' => 'required',
                    'package' => 'required'
                        ], 
                    [
                    'package.required' => 'Please provide your package.',
                    //'package_name.required' => 'Please provide your package name.',
                    //'name.required' => 'Please provide name.',
        ]);
        if ($validator->fails()) 
        {
            return $this->sendError('Validation Error.', $validator->errors()->first(), '200');
        }
        try 
        {
            $package = json_decode($input['package']);
            foreach ($package as $pkg) {
                $application = new Application();
                $application->name = $pkg->name;
                $application->package_name = $pkg->package_name;
                $application->install_date =  isset($pkg->install_date) ? date("Y-m-d" ,  strtotime($pkg->install_date)) : NULL;
                $application->user_id = $user->user_id;
                $application->licenses_id = $user->id;
                $application->save();
            }
            if ($application) {
                $data = (object)[];
                return $this->sendResponse($data, $this->messageDefault('Application has been saved successfully.'));
            } else {
                return $this->sendError($this->messageDefault('Application details Can not save'), '', '200');
            }      
        } catch (\Exception $e) { 
            return $this->sendError($this->messageDefault('oops'));
        }
    }

    /**
     * Store store Application
     * 
     *
     * @param array() $user_id,$request_data()
     *
     * @return array() $responseArray
     */
    public function storeImages(Request $request) 
    {
         
        $input = $request->all();
        $user = $request->get('Auth');
        $validator = Validator::make($request->all(), 
                    [
                    'image' => 'required|mimes:png,jpg,jpeg|max:10000',
                        ], 
                    [
                    'image.required' => 'Please provide image.',
        ]);
        if ($validator->fails()) 
        {
            return $this->sendError('Validation Error.', $validator->errors()->first(), '200');
        }
        try 
        {
            $images = new UserImage();
            $images->time = isset($input['time']) ? $input['time'] : NULL;
            $images->date =  isset($input['date']) ? date("Y-m-d" ,  strtotime($input['date'])) : NULL;
            $images->user_id = $user->user_id;
            $images->licenses_id = $user->id;
            if (isset($_FILES['image']['name']) && $_FILES['image']['name'] != '') {
                $profile_photo = 'users_'.time() . '.' . request()->image->getClientOriginalExtension();
                request()->image->move(public_path('uploads/users/'), $profile_photo);
                $images['image'] = $profile_photo;
            }
            $images->save();
            if ($images) {
                $data = (object)[];
                return $this->sendResponse($data, $this->messageDefault('User Image has been saved successfully.'));
            } else {
                return $this->sendError($this->messageDefault('User Image details Can not save'), '', '200');
            }      
        } catch (\Exception $e) { 
            return $this->sendError($this->messageDefault('oops'));
        }
    }

    /**
     * Store Location
     * 
     *
     * @param array() $user_licence_id,$request_data()
     *
     * @return array() $responseArray
     */
    public function storeLocation(Request $request) 
    {
         
        $input = $request->all();
        $user = $request->get('Auth');
        $validator = Validator::make($request->all(), 
                [
                    'latitude' => 'required',
                    'longitude' => 'required',
                    'event_date' => 'required',
                    'event_time' => 'required',
                ],
                [
                    'latitude.required' => 'Please provide Latitude.',
                    'longitude.required' => 'Please provide Longitude.',
                    'event_date.required' => 'Please provide Date.',
                    'event_time.required' => 'Please provide Time.',
                ]
        );
        if ($validator->fails()) 
        {
            return $this->sendError('Validation Error.', $validator->errors()->first(), '200');
        }
        try 
        {
            $event_date = $input['event_date'];
            $event_date = Carbon::parse($event_date)->format('Y/m/d');
            $event_time = $input['event_time'];
            $event_time = Carbon::parse($event_time)->format('h:i');
            
            $application = new Location();
            $application->latitude = $input['latitude'];
            $application->longitude = $input['longitude'];
            $application->event_date =  $event_date;
            $application->event_time =  $event_time;
            $application->user_id = $user->user_id;
            $application->licenses_id = $user->id;
            
            $application->save();
            if ($application) {
                $data = (object)[];
                return $this->sendResponse($data, $this->messageDefault('Location has been saved successfully.'));
            } else {
				\Log::info('error in store location');
                return $this->sendError($this->messageDefault('Location details Can not save'), '', '200');
            }      
        } catch (\Exception $e) {
			\Log::info($e->getMessage());
            return $this->sendError($this->messageDefault('oops'));
        }
    }

    /**
     * Store store Contact
     * 
     *
     * @param array() $user_id,$request_data()
     *
     * @return array() $responseArray
     */
    public function storeContact(Request $request) 
    {
        $input = $request->all();
        $user = $request->get('Auth');
        $validator = Validator::make($request->all(), 
                    [
                        'detail' => 'required'
                    ], 
                    [
                    '   detail.required' => 'Please provide your detail.',
                    ]
            );
        if ($validator->fails()) 
        {
            return $this->sendError('Validation Error.', $validator->errors()->first(), '200');
        }
        try 
        {
			
            $detail = json_decode($input['detail']);
            foreach ($detail as $pkg) {
                $application = new Contact();
                $application->full_name = $pkg->name;
                $application->phone = $pkg->phone_no;
                $application->user_id = $user->user_id;
                $application->licenses_id = $user->id;
                $application->save();
            }
            if ($application) {
                $data = (object)[];
                return $this->sendResponse($data, $this->messageDefault('Contact has been saved successfully.'));
            } else {
                return $this->sendError($this->messageDefault('Contact details Can not save'), '', '200');
            }      
        } catch (\Exception $e) { 
            return $this->sendError($this->messageDefault('oops'));
        }
    }
    

    public function checkLicencekey(Request $request) 
    {
        $input = $request->all();
        $data = [];
        try {
            $validator = Validator::make($input, ['license_key' => 'required']);
            if ($validator->fails()) 
            {
                return $this->sendError('Validation Error.', $validator->errors()->first(), '200');
            } else {
                $query = UserLicense::query();
                $userLicenseExists =  $query->where('licenses_key', request('license_key'))->count();
                /*Check Authorise*/
                $userLicense =  $query->where('licenses_key', request('license_key'))->first();
                if ($userLicenseExists == 0) 
                {
                    return $this->sendError($this->messageDefault('Invalid license key'), '', '200');

                } elseif ($userLicense) 
                {
                    
                        $data = $userLicense;
                        $currentDate = date('Y-m-d');
                        //$expireDate =  date('Y-m-d', strtotime($currentDate. ' + '.$userLicense->validity_days.' days'));
                        $expiryDate = $data['expiry_date'];
                        
                        $date = Carbon::parse($expiryDate);
                        $now = Carbon::now();
                        $diff = $date->diffInDays($now);

                        if($currentDate <= $expiryDate){
                            $data1['licenses_key'] =$data['licenses_key'];
                            $data1['activate_date']=$data['activate_date'];
                            $data1['expiry_date']=$data['expiry_date'];
                            $data1['expire_in']=$diff+1;
                           return $this->sendResponse($data1, $this->messageDefault('licence Key'));    
                        }
                        else{
                            $data1['valid'] = false;
                           return $this->sendError('This License key is expired', '', '200');
                    }
            }
            }
        
        } catch (\Exception $e) {
            return $this->sendError($this->messageDefault('oops'), '', '200');
        }
    }
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateCountryStateCityInUsersTable extends Migration
{
    public function up()
    {
        Schema::table('users', function (Blueprint $table) {
            // Add them back as unsignedBigInteger
            $table->unsignedBigInteger('country')->after('address')->nullable();
            $table->unsignedBigInteger('state')->after('country')->nullable();
            $table->unsignedBigInteger('city')->after('state')->nullable();

            // Add foreign keys
            $table->foreign('country')->references('id')->on('countries')->onDelete('set null');
            $table->foreign('state')->references('id')->on('states')->onDelete('set null');
            $table->foreign('city')->references('id')->on('cities')->onDelete('set null');
        });
    }

    public function down()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropForeign(['country']);
            $table->dropForeign(['state']);
            $table->dropForeign(['city']);

            $table->dropColumn(['country', 'state', 'city']);            
        });
    }
}

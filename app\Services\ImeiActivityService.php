<?php

namespace App\Services;

use Spatie\Activitylog\Facades\LogActivity;

class ImeiActivityService
{
    public static function log($action, $user, $model = null, $data = [], $module = 'IMEI')
    {
        activity($module)
            ->causedBy($user)
            ->withProperties($data)
            ->log($action);
		
		/*
		[
                'imei' => $imei,
                'controller' => $controller,
                'method' => $method,
                'ip' => $request->ip(),
                'request' => $request->all(),
            ]
		*/
    }
}

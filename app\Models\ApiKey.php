<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ApiKey extends Model
{
    use HasFactory;

    protected $table = 'api_keys';
	
	protected $fillable = [
		'name',
		'company_name',
		'ip_address',
		'website_url',
		'key',
		'imei_limit',
		'imei_usage'
	];
	
	// Check if more IMEIs can be added
    public function canAddIMEI()
    {
        return $this->imei_usage < $this->imei_limit;
    }

    // Increment IMEI usage
    public function incrementUsage()
    {
        if ($this->canAddIMEI()) {
            $this->increment('imei_usage');
            return true;
        }
        return false;
    }
	
	// Get remaining IMEI usage
    public function getRemainingUsageAttribute()
    {
        return max($this->imei_limit - $this->imei_usage, 0);
    }
	
	public function PhoneQr()
	{
		return $this->hasMany(NewPhoneQr::class);
	}		
	
}

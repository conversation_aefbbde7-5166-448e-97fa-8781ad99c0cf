<?php

namespace App\Http\Requests\Backend;


use Illuminate\Foundation\Http\FormRequest;
use \Illuminate\Http\Request;

class UpdateUserProfile extends FormRequest
{

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(Request $request)
    {
		$id = $this->route('id');
        return [
            'username' => 'required|string|max:100',
            'email' => 'required|email|regex:/^([a-z0-9\+_\-]+)(\.[a-z0-9\+_\-]+)*@([a-z0-9\-]+\.)+[a-z]{2,6}$/ix|unique:users,email,' . $id,
            'mobile'=> 'required|string|max:15|unique:users,mobile,' . $id,
            'address' => 'nullable|string',
            'country' => 'required|string',
            'state' => 'required|string',
            'city' => 'required|string',
            'pincode' => 'required|string',
			'gst_number' => 'required|regex:/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/',
            'plan' => 'required|string',
            'fcm_token' => 'nullable|string',
			'device_id' => 'nullable|string',
            'total_keys' => 'required|integer',
            'price_per_key' => 'required|numeric',
            'total_price' => 'required|numeric',
           
        ];
    }
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'username.required' => 'Please enter your user name',
            'email.required' => 'Please enter your email address',
            'mobile.required' => 'Please enter your contact no',
            'city.required' => 'Please enter your city',
            'state.required' => 'Please enter your state',
            'zipcode.required' => 'Please enter your zipcode',
            'country.required' => 'Please enter your country',
            'address_line1.required' => 'Please enter your address 1',
        ];
    }

}

<?php

namespace App\Http\Controllers\Api\V1;

use App\Models\SellerList;
use App\Models\SellerUsage;
use App\Models\DevicePass;
use App\Models\ApiKey;
use App\Models\NewPhoneQr as PhoneQr;
use App\Models\DeviceInfo;
use App\Models\PhoneDeviceLocation;
use App\Models\PhoneDeviceSim;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Traits\ApiGlobalFunctions;
use DB;
use Validator;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use App\Services\FireBaseService;
use App\Services\ImeiActivityService;

class AdminController extends Controller {

    use ApiGlobalFunctions;

	protected $androidService;
	protected $firebaseService;

	public function __construct()
    {
    }

    /**
     * Send QR
     *
     * @return \Illuminate\Http\Response
     */
    public function createQr(Request $request) 
    {
		
        $input = $request->all();
        $company = ApiKey::where(['company_name'=>$request->client_name, 'key'=>$request->header('x-api-key')])->first();		
        $data = [];

        try {

            $validator = Validator::make($input, ['imei' => 'required', 'mobile'=>'required']);

            if ($validator->fails()) 
            {
                return $this->sendError('Validation Error.', $validator->errors()->first(), '200');
            } else {

				$imei = request('imei');

				//get Qrcode which is common for all
				$qrCode = PhoneQr::first()->image;

				$phoneQr = PhoneQr::where('imei', $imei)->first();

				if(!$phoneQr) {
					
					if(!$company->canAddIMEI())
						return $this->sendError('IMEI limit exceeded! You have reached the maximum number of allowed device registrations. Please contact support for further assistance.', $validator->errors()->first(), '200');
					
					$phoneQr = new PhoneQr;
					$phoneQr->company_id = $company->id;
					$phoneQr->imei = $imei;
					$phoneQr->retailer_mobile = $request->mobile;
					$phoneQr->image = $qrCode;
					$phoneQr->save();
					
					//Increase IMEI Usage
					$company->incrementUsage();
				}

				//imei used in device info
				$deviceExist = PhoneQr::where(['imei'=>$imei, 'status'=>'used'])->first();
				if($deviceExist) {
					return response()->json(['error' => 'IMEI already used'], 400);
				}
				
				$qrCodeUrl = asset('storage/' . $qrCode);
				
                $data = [
					'qrcode' => $qrCodeUrl,
					'total_limit' => $company->imei_limit,
					'remaining_usage' => $company->remaining_usage,
				];

                $activityData = [
                    'imei' => $imei,
                    'request' => $request->all()
                ];
                ImeiActivityService::log('Generate QR', $company, null, $activityData);

				return response()->json($data);
                
            }
        } catch (\Exception $e) {

			\Log::info('Exception: '.$e->getMessage());

            $activityData = [
				'imei' => $imei,
				'request' => $e->getMessage()
			];
			ImeiActivityService::log('Generate QR',$company, null, $activityData);

            return $this->sendError($this->messageDefault('oops'), '', '400');
        }
    }
	
	public function updateDevice(Request $request) 
    {
		$input = $request->all();
        $data = [];
		$user = ApiKey::where(['company_name'=>$request->client_name, 'key'=>$request->header('x-api-key')])->first();

		\Log::info("AdminV2Controller: Request Data Before updateDevice: " . json_encode($request->all()));

        try {

            $validator = Validator::make($input, ['imei' => 'required','type' => 'required']);

            if ($validator->fails()) 
            {
                return $this->sendError('Validation Error.', $validator->errors()->first(), '200');
            } else {
				$type 		= request('type');
				$imei 		= request('imei');

				if (is_numeric($imei)) {
					$imei = (int) $imei; // Cast to integer for safety
				} else {
					return response()->json(['error' => 'Invalid Imei'], 400);
				}

				$deviceExist = DeviceInfo::where('imei', $imei)->first();
                
				if ($deviceExist) 
				{				
					$deviceId			= $deviceExist->id;						
					$firebase_token		= $deviceExist->firebase_token;

					if(is_null($deviceId)){
						return response()->json(['error' => 'Please check IMEI again'], 400);
					}
					
					$deviceType = json_decode($type,true);

					if(is_array($deviceType) && count($deviceType)>0 && count($deviceType[0]) > 0){
						
						foreach($deviceType as $types => $typeValue){
							$title = 'NOTIFY';
							$body = 'You have a new update!';
							if(isset($typeValue['LOCK'])){
								if($typeValue['LOCK']){
									$command = 'LOCK';
								}else{
									$command = 'UNLOCK';
								}
								//$this->sellerUsages($deviceId,$userId,$command);
								if($typeValue['LOCK']) {
									$commands = [
										'command_1' => 'LOCK'
									];										
									$installType='KIOSK';
								}else{																		
									$commands = [
										'command_1' => 'UNLOCK'
									];
									$installType='FORCE_INSTALLED';										
								}									
								
							}else if(isset($typeValue['APPDISABLE'])){
								if($typeValue['APPDISABLE']){
									$command = 'APPDISABLE';
								}else{
									$command = 'APPENABLE';
								}
								$commands = [
									'command_1' => $command
								];
								//$this->sellerUsages($deviceId,$userId,$command);
								
							}
							else if(isset($typeValue['DISABLEFORMAT'])){
								if($typeValue['DISABLEFORMAT']){
									$command = 'DISABLEFORMAT';
								}else{
									$command = 'ENABLEFORMAT';
								}
								$commands = [
									'command_1' => $command
								];
								//$this->sellerUsages($deviceId,$userId,$command);
								
							}
							elseif(isset($typeValue['WALLPAPERDISABLE'])){

								$wallpaperimage=NULL;
								
								if(!$typeValue['WALLPAPERDISABLE']){
									$command = 'WALLPAPERDISABLE';
								}else{
									$command = 'WALLPAPERENABLE';
									$validator = Validator::make($input, ['wallpaper_image' => 'required|max:102040'], ['wallpaper_image.required' => 'Please upload a wallpaper image']);
									if ($validator->fails()) 
									{
										return $this->sendError('Validation Error.', $validator->errors()->first(), '200');
									}
									if ($request->file('wallpaper_image')) {

										$any = $request->file('wallpaper_image');
										$file_extension = $any->getClientOriginalExtension();
										$file_name = $deviceId . '_wallpaper_image_'.date('dmyhi').'.'. $file_extension;
										$destination_path = storage_path("app/wallpaper");
										$any->storeAs('public/wallpaper', $file_name);

										$wallpaperimage= asset('storage/wallpaper/')."/".$file_name;
									}
								}
								if(isset($typeValue['LOCK'])) {
									$commandArray = [
										'command_2' => $command,
										'imagePath' => $wallpaperimage
									];		
									$commands = array_merge($commands,$commandArray);

								}else{
									$commands = [
										'command_1' => $command,
										'imagePath' => $wallpaperimage
									];		
									
								}
								//$this->sellerUsages($deviceId,$userId,$command,$wallpaperimage);
								
							}		
							
							elseif(isset($typeValue['REBOOT'])){
								$command = 'REBOOT';
								$commands = [
									'command_1' => 'REBOOT'
								];	
								//$this->sellerUsages($deviceId,$userId,$command);
								
							}
							elseif(isset($typeValue['CAMERADISABLE'])){
								if($typeValue['CAMERADISABLE']){
									$command = 'CAMERADISABLE';
								}else{
									$command = 'CAMERAENABLE';
								}
								$commands = [
									'command_1' => $command
								];	
								//$this->sellerUsages($deviceId,$userId,$command);
								
							}
							elseif(isset($typeValue['USBDISABLE'])){
								if($typeValue['USBDISABLE']){
									$command = 'USBDISABLE';
								}else{
									$command = 'USBENABLE';
								}
								$commands = [
									'command_1' => $command
								];	
								//$this->sellerUsages($deviceId,$userId,$command);
								
							}

							elseif(isset($typeValue['CHANGEPASSWORD'])){
								
								$newpassword 		= request('newpassword');								
								
								$validPass = $this->isValidPassword($newpassword);								
								if($validPass){			
									//$this->sellerUsages($deviceId,$userId,$type);
									$msg='Password changed successfully';
									$devicePass = new DevicePass;
									$devicePass->device_id = $deviceId;
									$devicePass->password = $newpassword;
									//$devicePass->user_id=$userId;
									$devicePass->save();
									$commands = [
										'command_1' => 'CHANGEPASSWORD',
										'password' => $newpassword
									];	
								}else{
									return response()->json(['error' => "Password is invalid. It must be at least 6 characters long and contain both letters and numbers and at least one special character."], 400);
								}
							}

							//Uninstall command and delete imei
							elseif(isset($typeValue['UNINSTALL'])){

								$command = 'UNINSTALL';
								$commands = [
									'command_1' => 'UNINSTALL'
								];

								//delete imei from qr table
								PhoneQr::where(['imei'=>$imei])->delete();
								DeviceInfo::where(['imei'=>$imei])->delete();
								
							}

                            //Format
							elseif(isset($typeValue['FORMAT'])){

								$command = 'FORMAT';
								$commands = [
									'command_1' => 'FORMAT'
								];
								
								//delete imei from qr table
								PhoneQr::where(['imei'=>$imei])->delete();
								DeviceInfo::where(['imei'=>$imei])->delete();
							}

							//Change device default password
                            elseif(isset($typeValue['DEFAULTPASS'])){

                                $validator = Validator::make(
                                    $input, 
                                    ['default_password' => 'required|max:8']
                                );

                                if ($validator->fails()) 
                                {
                                    return $this->sendError('Validation Error.', $validator->errors()->first(), '200');
                                }								
								$defaultPassword = request('default_password');
								$deviceExist->update(['default_password'=>$defaultPassword]);
								$commands = [
									'command_1' => 'DEFAULTPASS',
									'password' => $defaultPassword
								];
								
							}

							//Change Retailer Mobile
							elseif(isset($typeValue['RETIAILERNO'])){

                                $validator = Validator::make(
                                    $input, 
                                    ['retailer_mobile' => 'required|max:20']
                                );

                                if ($validator->fails()) 
                                {
                                    return $this->sendError('Validation Error.', $validator->errors()->first(), '200');
                                }
								
								$retailerMobile = request('retailer_mobile');                                					
								$deviceExist->update(['mobile'=>$retailerMobile]);
								$commands = [
									'command_1' => 'RETIAILERNO',
									'mobile' => $retailerMobile
								];
								
							}

							//Change Onwer Mobile
							elseif(isset($typeValue['OWNERNO'])){

                                $validator = Validator::make(
                                    $input, 
                                    ['owner_mobile' => 'required|max:20']
                                );

                                if ($validator->fails()) 
                                {
                                    return $this->sendError('Validation Error.', $validator->errors()->first(), '200');
                                }
								
								$ownerMobile = request('owner_mobile');                           					
								$deviceExist->update(['owner_mobile'=>$ownerMobile]);
								$commands = [
									'command_1' => 'OWNERNO',
									'mobile' => $ownerMobile
								];
								
							}

							//Suspend Packages
							elseif(isset($typeValue['SUSPEND'])){

								$validator = Validator::make($input, 
									['packages' => 'required']
								);

								if ($validator->fails()) 
								{
									return $this->sendError('Validation Error.', $validator->errors()->first(), '200');
								}	

								if($typeValue['SUSPEND']){
									$command = 'SUSPEND';
								}else{
									$command = 'UNSUSPEND';
								}

								$commands = [
									'command_1' => $command,
									'packages' => $request->packages
								];
								//com.whatsapp, com.facebook.katana, com.google.android.youtube
								
							}

						}
						if(isset($commands)){
							\Log::info("firebaseresponse commands: ". json_encode($commands));
							$this->firebaseService = new FireBaseService();
							$firebaseResponse = $this->firebaseService->sendCommand($commands,$firebase_token,$title,$body,'1');
							\Log::info("firebaseresponse token: ".$firebase_token." :response: " . json_encode($firebaseResponse));
						}

						$activityData = [
							'imei' => $request->imei,
							'request' => $request->all()
						];
						ImeiActivityService::log('Sent Command',$user, null, $activityData);
						
						$data = [
							'status' => 'SUCCESS'
						];

						return response()->json($data);

					}else{

						return response()->json(['error' => 'Please check type data'], 400);

					}
					
				} else {
						return $this->sendError('Device not exist. Please contact admin.', '', '200');
				}
            }
        } catch (\Exception $e) {

			$activityData = [
				'imei' => $request->imei,
				'request' => $e->getMessage()
			];
			ImeiActivityService::log('Error in Command',$user, null, $activityData);
			
			\Log::info('Exception: '.$e->getMessage());
            return $this->sendError($this->messageDefault('oops'), '', '400');
        }
    }

	public function deleteDevice(Request $request) 
    {
		
        $input = $request->all();
        $data = [];
		\Log::info("AdminV2Controller: Request Data Before deleteDevice: " . json_encode($request->all()));
        try {
            $validator = Validator::make($input, ['user_id' => 'required','imei' => 'required']);
            if ($validator->fails()) 
            {
                return $this->sendError('Validation Error.', $validator->errors()->first(), '200');
            } else {
				$userId 	= request('user_id');
				if($userId == '0'){
					return $this->sendError('Validation Error. UserId invalid',  '200');
				}
				$imei 		= request('imei');
				if (is_numeric($imei)) {
					$imei = (int) $imei; // Cast to integer for safety
				} else {
					return response()->json(['error' => 'Invalid Imei'], 400);
				}
				$deviceExist 	=  Device::where('devices.imei', $imei)->where('devices.user_id', $userId)->first();
                $userExist 		=  SellerList::where('user_id', $userId)->first();
                if ($userExist) 
                {
					if ($deviceExist) 
                	{				
						$deviceId		= $deviceExist->id;
						if(is_null($deviceId)){
							return response()->json(['error' => 'Please check IMEI again'], 400);
						}
						
						$updatedFields = array();
						
						\Log::info('deleteDevice: '.$deviceId);
						$msg='Device Deleted successfully';
						
						Device::find($deviceId)->delete();
						
						unset($policyResponse);unset($policyResDecode);
						$data = [
							'status' => 'SUCCESS',
							'message' => $msg
						];
						return response()->json($data);
                        
					} else {
						return $this->sendError('Device not exist. Please contact admin.', '', '200');
					} 
                    
                } else {
                     return $this->sendError('User not exist. Please contact admin.', '', '200');
                }
            }
        } catch (\Exception $e) {
			\Log::info('Exception: '.$e->getMessage());
            return $this->sendError($this->messageDefault('oops'), '', '400');
        }
    }	
	function isValidPassword($password) {
		$validator = Validator::make(['password' => $password], [
			'password' => 'nullable|max:12'
		]);

		return $validator->passes();
	}	
	public function sendNotification(Request $request) 
    {
        $input = $request->all();
        $data = [];

		\Log::info("AdminV2Controller: Request Data Before sendNotification: " . json_encode($request->all()));

        try {

            $validator = Validator::make($input, ['imei' => 'required','type' => 'required','title' => 'required','message' => 'required']);

            if ($validator->fails()) 
            {
                return $this->sendError('Validation Error.', $validator->errors()->first(), '200');
            } else {
				$type 		= request('type');
				$imei 		= request('imei');
				$body 		= request('message');
				$title 		= request('title');

				if (is_numeric($imei)) {
					$imei = (int) $imei; // Cast to integer for safety
				} else {
					return response()->json(['error' => 'Invalid Imei'], 400);
				}

				$deviceExist 	=  DeviceInfo::where('imei', $imei)
									->select('firebase_token as firebase_token','id as id','user_id')->first();
				
                
					if ($deviceExist) 
                	{				
						$deviceId		= $deviceExist->id;
						$firebase_token		= $deviceExist->firebase_token;
						
						if(is_null($deviceId)){
							return response()->json(['error' => 'Please check IMEI again'], 400);
						}
						
						$this->firebaseService = new FireBaseService();

						$firebaseResponse = $this->firebaseService->sendNotification($firebase_token,$title,$body,'1');

						\Log::info("sendNotification token: ".$firebase_token." :response: " . json_encode($firebaseResponse));
                        
						if($firebaseResponse){
							$msg = "Notification sent";
							$data = [
								'status' => 'SUCCESS',
								'message' => $msg
							];
							return response()->json($data);                        
						}

					} else {
						return $this->sendError('Device not exist. Please contact admin.', '', '200');
                	} 
                    
                
            }
        } catch (\Exception $e) {
			\Log::info('Exception: '.$e->getMessage());
            return $this->sendError($this->messageDefault('oops'), '', '400');
        }
    }
	
	public function fetchDetails(Request $request) 
    {
		set_time_limit(0);

        $input = $request->all();
        $data = [];

		\Log::info("AdminV2Controller: Request Data Before fetchDetails: " . json_encode($request->all()));

        try {

            $validator = Validator::make($input, ['imei' => 'required','type' => 'required']);

            if ($validator->fails()) 
            {
                return $this->sendError('Validation Error.', $validator->errors()->first(), '200');
            } else {

				$type 		= request('type');
				$imei 		= request('imei');

				if (is_numeric($imei)) {
					$imei = (int) $imei; // Cast to integer for safety
				} else {
					return response()->json(['error' => 'Invalid Imei'], 400);
				}

				$deviceExist = DeviceInfo::where('imei', $imei)
									->select('firebase_token as firebase_token','id as id','user_id')->first();
                
				if ($deviceExist) 
				{
					$deviceId		= $deviceExist->id;
					$firebase_token		= $deviceExist->firebase_token;

					if(is_null($deviceId)){
						return response()->json(['error' => 'Please check IMEI again'], 400);
					}

					$phoneModel = $type=='LOCATION' ? new PhoneDeviceLocation : new PhoneDeviceSim;

					// Check daily request count
					$todayCount = $phoneModel->where('device_id', $deviceId)
						->whereDate('created_at', Carbon::today())
						->count();

					if ($todayCount >= 5) {
						return $this->sendError('Daily limit of 5 requests exceeded for this IMEI.', '', '200');
					}

					$data =  $this->fetchDeviceData($type,$deviceId); //within 5minute
						
					if($data){
						return response()->json($data);
					}
					
					$this->firebaseService = new FireBaseService();

					$commands = [
						'command_1' => $type,
					];	

					$firebaseResponse = $this->firebaseService->sendCommand($commands,$firebase_token,'','','1');

					\Log::info("fetchDetailsv2: sendCommand token: ".$firebase_token." :response: " . json_encode($firebaseResponse));

					if(!$firebaseResponse){							
						return $this->sendError('Some issue occured. Please contact admin.', '', '200');                  
					}

					sleep(2);

					for($loop=1;$loop<=5;$loop++){						
						$data = $this->fetchDeviceData($type,$deviceId);
						
						if($data){
							return response()->json($data);
							break;
						}	
						sleep(2);
					}

					return $this->sendResponse([], 'Success');

				} else {
					return $this->sendError('Device not exist. Please contact admin.', '', '200');
				}                    
                
            }
        } catch (\Exception $e) {
			\Log::info('Exception: '.$e->getMessage());
            return $this->sendError($this->messageDefault('oops'), '', '400');
        }
    }
	
	public function fetchDeviceData($type,$deviceId){

		if($type=='LOCATION'){			
			$deviceLocations = PhoneDeviceLocation::where('device_id',$deviceId)
				->where('updated_at', '>=', Carbon::now()->subMinutes(5))->first();
			
			if($deviceLocations){
				return $data = [
					'status' => 'SUCCESS',
					'latitude' => $deviceLocations->lat,
					'longitude' => $deviceLocations->long,
					'datetime' => $deviceLocations->datetime
				];
			}
		}elseif($type=='SIM'){

			$deviceSim = PhoneDeviceSim::where('device_id',$deviceId)
				->where('updated_at', '>=', Carbon::now()->subMinutes(5))->first();
			if($deviceSim){
				return $data = [
					'status' => 'SUCCESS',
					'mobile1' => $deviceSim->mobile1,
					'mobile2' => $deviceSim->mobile2
				];
			}
		}
		return false;
	}
	
	public function sellerUsages($deviceId,$userId,$type,$wallpaperimage=NULL){
		$sellerUsage = new SellerUsage;
		$sellerUsage->user_id=$userId;
		$sellerUsage->device_id=$deviceId;
		$sellerUsage->command=$type;
		$sellerUsage->wallpaper_image=$wallpaperimage;
		$sellerUsage->save();
		unset($sellerUsage);
	}
    
	public function checkSellerUsages($deviceId,$userId){
		$currentDate = Carbon::today();
		return $checkSellerUsage = SellerUsage::where('device_id',$deviceId)
							->where('user_id',$userId)
							->whereDate('created_at', $currentDate)
							->count();
		
	}
	
}
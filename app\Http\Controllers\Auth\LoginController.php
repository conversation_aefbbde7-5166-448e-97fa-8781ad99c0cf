<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Providers\RouteServiceProvider;
use Illuminate\Foundation\Auth\AuthenticatesUsers;
use Illuminate\Http\Request;

class LoginController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Login Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles authenticating users for the application and
    | redirecting them to your home screen. The controller uses a trait
    | to conveniently provide its functionality to your applications.
    |
    */

    use AuthenticatesUsers;

    /**
     * Where to redirect users after login.
     *
     * @var string
     */
    //protected $redirectTo = 'admin/dashboard';

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest')->except('logout');
    }

    public function login(Request $request)
    {
        $inputVal = $request->all();
		
        $this->validate($request, [
            'email' => 'required|email',
            'password' => 'required',
        ]);
		
        if (auth()->attempt(array('email' => $inputVal['email'], 'password' => $inputVal['password']))) {            
			if (auth()->user()->is_admin == 1) {
				return redirect()->route('admin.dashboard')->with('alert-success','You have successfully logged in');
			} elseif(auth()->user()->hasAnyRole(['super_admin', 'admin', 'national_distributor', 'super_distributor', 'distributor']) ) {
				return redirect()->route('admin.admin-dashboard')->with('alert-success','You have successfully logged in');
			} elseif(auth()->user()->role == 'agent') {
				return redirect()->route('admin.imei')->with('alert-success','You have successfully logged in');
			} else {
				auth()->logout(); // Ensure unauthorized users are logged out
				return redirect()->route('admin.login')->with('alert-danger', 'You are not authorized to access this section.');
			}            
        } else {
            return redirect()->route('admin.login')->with('alert-danger', 'Invalid credentials.');
        }
    }
	
	protected function loggedOut(Request $request)
	{
		return redirect('/admin/authentication/login'); // Change to your desired route
	}
	
	// Override redirectTo method
    protected function redirectTo()
    {
        $user = Auth::user();

        // Example conditions
        if ($user->is_admin == 1) {
            return '/admin/dashboard';
        } elseif ($user->role == 'admin') {
            return '/admin/admin-dashboard';
        }

        return '/admin/authentication/login';
    }

}

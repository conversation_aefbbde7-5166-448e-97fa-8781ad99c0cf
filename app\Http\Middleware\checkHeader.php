<?php

namespace App\Http\Middleware;

use Closure;
use App\Http\Traits\ApiGlobalFunctions;

class checkHeader {

    use ApiGlobalFunctions;

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next) {
        $authInformation = apache_request_headers();
        $APP_VERSION = 1; 
        if (isset($authInformation['Version-Code']) && isset($authInformation['Device-Type']) && $authInformation['Device-Type'] == 'iOS') 
        {
            if (!empty($authInformation['Version-Code']) && $authInformation['Version-Code'] == $APP_VERSION) {
                return $next($request);
            } else {
                return $this->sendErrorForVersion($this->messageDefault('A new version is available. Please update the app.'),'', '200');
            }
        } else if (isset($authInformation['Version-Code']) && isset($authInformation['Device-Type']) && $authInformation['Device-Type'] == 'android') {
            if (!empty($authInformation['Version-Code']) && $authInformation['Version-Code'] == $APP_VERSION) {
                return $next($request);
            } else { 
                return $this->sendErrorForVersion($this->messageDefault('A new version is available. Please update the app.'),'', '200');
            }   
        } else {
            return $this->sendError($this->messageDefault('You are not authorize to access.') ,'','401');
        }
    }

}

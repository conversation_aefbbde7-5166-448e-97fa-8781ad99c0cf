<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddBooleanColumnsToCustomersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('customers', function (Blueprint $table) {
            $table->boolean('is_camera_enabled')->default(true)->after('reg_date'); // Camera On/Off
			$table->boolean('is_usb_enabled')->default(true)->after('is_camera_enabled'); // USB On/Off
            $table->boolean('is_locked')->default(false)->after('is_usb_enabled'); // Locked/Unlocked
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('customers', function (Blueprint $table) {
            $table->dropColumn(['is_camera_enabled', 'is_locked', 'is_usb_enabled']);
        });
    }
}

<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\CustomerEmergencyContact;
use App\Models\Customer;
use Illuminate\Support\Facades\Crypt;
use App\Services\ImeiActivityService;

class CustomerEmergencyContactController extends Controller
{
    // Store Contact info
    public function store(Request $request)
    {
		
		$request->validate([
			'imei_1' => 'required|string',
			'name' => 'required|string|max:100',
			'mobile' => ['required', 'regex:/^[6-9]\d{9}$/'],
		]);

        try {
			
			$imei = $request->imei_1;
			
            // ✅ Check in `customers` table
            $customer = Customer::where('imei_1', $imei)
                ->select('id', 'retailer_id', 'firebase_token', 'device_status')
                ->first();

            if (!$customer) {
                return $this->sendError('Customer does not exist. Please contact admin.', '', 200);
            }
			
			$customerId = Crypt::decryptString($customer->id);
			
			$contacts = CustomerEmergencyContact::with('customer')
				->where('customer_id', $customerId)
            ->orderBy('created_at')->get();
			
			$activityData = [
				'imei' => $request->imei_1,
				'request' => $request->all()
			];
            ImeiActivityService::log('Add Emergency Contact', auth()->user(), null, $activityData, 'customer');

            if ($contacts->count() < 4) {
                // Add new contact directly
                $data = CustomerEmergencyContact::create([
                    'customer_id' => $customerId,
                    'name' => $request->name,
                    'phone' => $request->mobile,
                ]);
            } else {
                // Overwrite the oldest one
                $oldest = $contacts->first();
                $oldest->update([
                    'name' => $request->name,
                    'phone' => $request->mobile,
                ]);
				$data = CustomerEmergencyContact::find($oldest->id);
            }

            return response()->json(['message' => 'Contact details stored successfully', 'data' => $data], 200);
        } catch (\Exception $e) {
			$activityData = [
				'imei' => $request->imei_1,
				'request' => $e->getMessage()
			];
            ImeiActivityService::log('Add Emergency Contact Error', auth()->user(), null, $activityData, 'customer');
			
            return response()->json(['message' => $e->getMessage()], 200);
        }
    }

    // Get Contact info
    public function index($encryptedId)
    {
        try {
			$id = Crypt::decryptString($encryptedId);
		} catch (\Exception $e) {
			return response()->json(['message' => 'Invalid ID'], 200);
		}

        $data = CustomerEmergencyContact::where('customer_id', $id)
            ->orderBy('created_at')
            ->get(['name','phone','created_at']);

        return response()->json([
            'message' => 'Contact details retrieved successfully',
            'data' => $data
        ], 200);
    }

    // Soft delete Contact info
    /* public function destroy($id)
    {
        try {
            $sim = CustomerEmergencyContact::findOrFail($id);
            $sim->delete(); // Soft delete
            return response()->json(['message' => 'Contact details deleted successfully'], 200);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Contact details not found'], 200);
        }
    } */
}

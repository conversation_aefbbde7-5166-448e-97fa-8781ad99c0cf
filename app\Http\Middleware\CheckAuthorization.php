<?php

namespace App\Http\Middleware;

use Closure;
use App\Http\Traits\ApiGlobalFunctions;
use DB;

class CheckAuthorization {

    use ApiGlobalFunctions;

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next) 
    {
      
        $authInformation = apache_request_headers();
        if (isset($authInformation['Authorization']) && !empty($authInformation['Authorization'])) 
        {
            $token = str_replace("Bearer", " ", $authInformation['Authorization']);
            $token = trim($token);
            $user = DB::table('user_licenses')->where('api_token', $token)->first();

            if (empty($user)) {

                return $this->sendErrorForAuth($this->messageDefault('You are not authorize to access any more.'),'','200');
            } else {

                if ($user->is_used == 0) {
                    return $this->sendErrorForAuth($this->messageDefault('You account has been deactivated,Please contact the admin.'),'', '200');
                } else {
                    $request->attributes->set('Auth', $user);
                    return $next($request);
                }
            }
        } else {
           
            return $this->sendErrorForAuth($this->messageDefault('You are not authorize to access.'),'', '200');
        }
    }

}


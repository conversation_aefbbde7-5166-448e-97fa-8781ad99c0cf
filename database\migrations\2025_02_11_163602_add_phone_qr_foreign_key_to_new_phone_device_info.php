<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddPhoneQrForeignKeyToNewPhoneDeviceInfo extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('new_phone_device_info', function (Blueprint $table) {
            $table->Integer('phone_qr_id')->after('id')->nullable(); // Adjust data type if needed
        	$table->foreign('phone_qr_id')->references('id')->on('new_phone_qr')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('new_phone_device_info', function (Blueprint $table) {
            $table->dropForeign(['phone_qr_id']);
        	$table->dropColumn('phone_qr_id');
        });
    }
}

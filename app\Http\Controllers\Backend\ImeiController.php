<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\DeviceInfo;
use App\Models\NewPhoneQr;
use App\Models\ApiKey;
use App\Models\Customer;
use App\Services\FireBaseService;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ImeiController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
		$companies = ApiKey::where(['is_active'=>1])->whereNotNull('ip_address')->pluck('company_name','company_name')->toArray();
		
		$data = collect(); // Default empty collection
		
		// Validation for start and end date
		$request->validate([
			'company' => 'nullable|string|in:' . implode(',', $companies),
			'start_date' => 'nullable|date',
			'end_date' => 'nullable|date|after_or_equal:start_date',
		]);
		
		// Ensure company is required when filtering
    	if ($request->has('company')) {
			
			// If user is an agent, validate that search is not empty when company is selected
			if (Gate::allows('agent-access')) {
				$validator = Validator::make($request->all(), [
					'search' => 'required|string',
				], [
					'search.required' => 'IMEI Should not be empty.',
				]);

				if ($validator->fails()) {
					return back()->withErrors($validator)->withInput();
				}
			}
			
			if ($request->company == 'paisacops') {
		
				$query = NewPhoneQR::withTrashed()
					->with([
						//'PhoneQr:id,company_id,status,deleted_at',
						'company:id,name'
					])
					->orderBy('id', 'desc');

				
				if ($request->has('search') && !empty($request->search)) {
					if(Gate::allows('super-admin-access')) {
						$query->where('imei', 'LIKE', "%{$request->search}%");
					} else {
						if (!empty($request->company) && !empty($request->search)) {
                            $query->where('imei', $request->search);
                        } else {
                            // Don't apply search if company or imei missing
                            $query->whereRaw('1 = 0'); // No results
                        }
					}
				}				
				
			} elseif ($request->company == 'lockmaster') {

				$query = Customer::withTrashed()->orderBy('id', 'desc');

                if ($request->has('search') && !empty($request->search)) {
					if(Gate::allows('super-admin-access')) {
						$query->where('imei_1', 'LIKE', "%{$request->search}%");
					} else {
						if (!empty($request->company) && !empty($request->search)) {
                            $query->where('imei_1', $request->search);
                        } else {
                            // Don't apply search if company or imei missing
                            $query->whereRaw('1 = 0'); // No results
                        }
					}
				}
			}
			
			if (!empty($request->start_date) && !empty($request->end_date)) {
                $query->whereBetween('created_at', [$request->start_date, $request->end_date]);
            }

        	$data = $query->paginate(20);
			
			// Add sequence number starting from (current_page - 1) * per_page + 1
			$start = ($data->currentPage() - 1) * $data->perPage() + 1;

			$data->getCollection()->transform(function ($item, $key) use ($start) {
				$item->sequence = $start + $key;
				return $item;
			});
		}
		
        return view('backend.imei.index', compact('data','companies'));
    }

	public function sendCommand(Request $request)
	{
		$request->validate([
			'company' => 'required|string',
			'imei' => 'required|string',
			'type' => 'required|string',
		]);

		DB::beginTransaction();

		try {
			$company = $request->company;
			$imei = $request->imei;
			$type = $request->type;

			if ($company === 'paisacops') {
				$deviceInfo = DeviceInfo::withTrashed()->where('imei', $imei)->first();
			} else {
				$deviceInfo = Customer::withTrashed()->where('imei_1', $imei)->first();

				if($type == 'LOCK') {
					$deviceInfo->update(['is_locked' => 1]);
				} else if($type == 'UNLOCK') {
					$deviceInfo->update(['is_locked' => 0]);
				} else if($type == 'UNINSTALL') {
					$deviceInfo->update(['device_status' => 'uninstalled']);
					$deviceInfo->delete();
				} else if($type == 'FORMAT') {
					$deviceInfo->update(['device_status' => 'formatted']);
					$customer->delete();
				}
			}

			if (!$deviceInfo) {
				DB::rollBack(); // Optional here, since nothing is written
				return response()->json(['status' => false, 'message' => 'Device not found'], 404);
			}

			$title = 'Command Request';
			$body = ['command_1' => $type];			
			$firebase_token = $deviceInfo->firebase_token;

			$this->firebaseService = new FireBaseService();
			$firebaseResponse = $this->firebaseService->sendPushNotification($firebase_token, $title, $body, '1','customer',true);

			if($firebaseResponse !== true) {
				DB::rollBack();
				return response()->json(['status' => false, 'message' => 'Failed to send command'], 500);
			}

			DB::commit();

			return response()->json([
				'status' => true,
				'message' => 'Command sent successfully',
				'response' => $firebaseResponse
			]);

		} catch (\Throwable $e) {
			DB::rollBack();
			Log::error('Send Command Error: ' . $e->getMessage(), [
				'request' => $request->all(),
				'trace' => $e->getTraceAsString()
			]);

			return response()->json([
				'status' => false,
				'message' => 'Something went wrong. Please try again later.',
			], 500);
		}
	}

}

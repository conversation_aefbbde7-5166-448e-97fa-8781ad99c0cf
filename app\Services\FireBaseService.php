<?php

namespace App\Services;

use Kreait\Firebase\Messaging\CloudMessage;
use Kreait\Firebase\Messaging\Notification;

class FireBaseService
{

	protected $retailerFirebaseJson;
	protected $customerFirebaseJson;

	public function __construct()
    {
        $this->retailerFirebaseJson = base_path(env('RETAILER_FIREBASE_JSON')); // Set in .env
		$this->customerFirebaseJson = base_path(env('CUSTOMER_FIREBASE_JSON')); // Set in .env
    }

    public function sendCommand($commands,$token,$title,$body,$version=null)
	{
		// Create a Firebase Messaging instance
		try{
			if(!is_null($version)){
				\Log::info('versionfirebase: '.$version);
				$clientSecretPath  =  base_path('storage/app/google-services-firebase-v2.json');
				$messaging = (new \Kreait\Firebase\Factory())
				->withServiceAccount($clientSecretPath)
				->createMessaging();
			}else{
				$messaging = app('firebase.messaging');
			}
			
			$message = CloudMessage::fromArray([
				'token' => $token, // Replace with the target device's FCM token
				'data' => $commands
			]);

			$messaging->send($message);
			\Log::info('sendCommand firebase API response: success: '.json_encode($message) );

			return true;
		}catch (\Exception $e) {
			\Log::error('sendCommand firebase API Error: ' . $e->getMessage());
			return $e->getMessage();
		}
	}
	
	public function sendNotification($token,$title,$body,$version=null)
	{
		try{
			if(!is_null($version)){
				\Log::info('versionfirebase: '.$version);
				$clientSecretPath  =  base_path('storage/app/google-services-firebase-v2.json');
				$messaging = (new \Kreait\Firebase\Factory())
				->withServiceAccount($clientSecretPath)
				->createMessaging();
			}else{
				$messaging = app('firebase.messaging');
			}

			$message = CloudMessage::fromArray([
				'token' => $token, // Replace with the target device's FCM token
				'notification' => [
					'title' => $title,
					'body' => $body,
				],
			]);

			$messaging->send($message);
			\Log::info('sendNotification firebase API response: success' );

			return true;
		}catch (\Exception $e) {
			\Log::error('sendNotification firebase API Error: ' . $e->getMessage());
			return $e->getMessage();
		}
	}

	public function sendPushNotification($token, $title, $body, $version = null, $app, $is_command = false)
	{
		try {
			
			/*\Log::info('FCM Notification Attempt', [
				'token' => $token,
				'title' => $title,
				'body' => $body,
				'version' => $version
			]);*/

			if (!is_null($version)) {
				
				$clientSecretPath = $app=='retailer' ? $this->retailerFirebaseJson : $this->customerFirebaseJson;

				if (!file_exists($clientSecretPath)) {
					\Log::info('FCM Notification Sent : Firebase JSON file not found:', $clientSecretPath);
					throw new \Exception("Firebase JSON file not found: " . $clientSecretPath);
				}

				$messaging = (new \Kreait\Firebase\Factory())
					->withServiceAccount($clientSecretPath)
					->createMessaging();
			} else {
				$messaging = app('firebase.messaging');
			}

			if($is_command == true) {
				$message = CloudMessage::fromArray([
					'token' => $token,
					'data' => $body
				]);
			} else {
				$message = CloudMessage::fromArray([
					'token' => $token,
					'notification' => [
						'title' => $title,
						'body' => $body,
					],
				]);
			}

			$response = $messaging->send($message);

			\Log::info('FCM Notification Sent', ['response' => $response]);

			return true;
		} catch (\Throwable $e) {
			\Log::error('sendPushNotification Error: ' . $e->getMessage(), [
				'exception' => $e,
				'line' => $e->getLine(),
				'file' => $e->getFile()
			]);

			return $e->getMessage();
		}
	}


}

<?php

namespace App\Services;

use Google\Client;
use Google\Service\AndroidManagement;
use SimpleSoftwareIO\QrCode\Facades\QrCode;

class AndroidManagementService
{
    private $client;
    private $service;
	private $enterpriseId;

    public function __construct($credentialPath=null,$enterpriseId=null)
    {
		\Log::info('__construct: '.$credentialPath);		\Log::info('__construct: '.$enterpriseId);
        // Initialize Google Client
        $this->client = new Client();
        $this->client->setAuthConfig(base_path('storage/app/mobile-access-ownership.json'));
        $this->client->addScope(AndroidManagement::ANDROIDMANAGEMENT);
		if ($this->client->isAccessTokenExpired()) {
			$this->client->fetchAccessTokenWithAssertion();
		}
        // Initialize Android Management API Service
     	$this->service = new AndroidManagement($this->client);
		$this->enterpriseId = $enterpriseId;
		//$this->enterpriseId = 'LC01frgy0e';


    }

    /**
     * Create a new enterprise.
     */
    public function createEnterprise($enterpriseName)
    {
        $enterprise = new AndroidManagement\Enterprise([
            'enterpriseDisplayName' => $enterpriseName,
            'contactInfo' => ['email' => '<EMAIL>']
        ]);

        $operation = $this->service->enterprises->create(['projectId' => env('ANDROID_MANAGEMENT_API_PROJECT')], $enterprise);

        return $operation;
    }

    /**
     * Get a list of devices in an enterprise.
     */
    public function listDevices()
    {
		$name = "enterprises/{$this->enterpriseId}";  // Format the resource name for the enterprise
		$devices = [];
    	$nextPageToken = null;
		try{
        	 do {
            $response = $this->service->enterprises_devices->listEnterprisesDevices($name, ['pageToken' => $nextPageToken]);

            // Append devices to the list
            $devices = array_merge($devices, $response->getDevices());

            // Check if there's another page of results
            $nextPageToken = $response->getNextPageToken();
        } while ($nextPageToken);

        return $devices;
		} catch (\Google\Service\Exception $e) {
			\Log::error('Google API Error: ' . $e->getMessage());
			throw $e;
		}
	
    }

    /**
     * Get a device by device ID.
     */
    public function getDevice($deviceId)
    {
        return $this->service->enterprises_devices->get($this->enterpriseId, $deviceId);
    }

    // Add other methods for specific API calls as needed
	
	public function wipeDevice($deviceId)
	{
		$name = "enterprises/{$this->enterpriseId}/devices/{$deviceId}";

		try {
			// Trigger a wipe operation
			$response = $this->service->enterprises_devices->wipe($name);
			return $response;
		} catch (\Google\Service\Exception $e) {
			\Log::error('Google API Error: ' . $e->getMessage());
			return response()->json(['error' => 'Error wiping device: ' . $e->getMessage()], 500);
		}
	}
	
	public function setScreenLockPolicy()
	{
		$name = "enterprises/{$this->enterpriseId}/policies";
		$policy = new \Google\Service\AndroidManagement\Policy();

		// Set a screen lock policy (enforce screen lock with PIN)
		$policy->setPasswordRequired(true);
		$policy->setPasswordMinimumLength(6);  // Set a minimum length for the password

		try {
			$response = $this->service->enterprises_policies->patch($name, $policy);
			return $response;
		} catch (\Google\Service\Exception $e) {
			\Log::error('Google API Error: ' . $e->getMessage());
			return response()->json(['error' => 'Error setting lock policy: ' . $e->getMessage()], 500);
		}
	}
	
	public function lockDevice($deviceId)
	{
		$name = "enterprises/{$this->enterpriseId}/devices/{$deviceId}";

		try {
			// Prepare the device state update to lock the device
			$device = new \Google\Service\AndroidManagement\Device();
			$device->setState('LOCKED');  // Set the state to 'LOCKED' to lock the device

			// Update the device's state via the Android Management API
			$response = $this->service->enterprises_devices->patch($name, $device);

			return $response;
		} catch (\Google\Service\Exception $e) {
			\Log::error('Google API Error: ' . $e->getMessage());
			return response()->json(['error' => 'Error locking device: ' . $e->getMessage()], 500);
		}
	}
	
	public function createPolicy($policyId)
	{
		$packageName = env('ANDROID_ENTERPRISE_PACKAGE_NAME');
		//$policyId = "lockpolicy"; // Name of your policy
		$policyName = "enterprises/{$this->enterpriseId}/policies/{$policyId}";

		// Construct the policy data
		$policy = new \Google\Service\AndroidManagement\Policy([
			"name" => $policyName,
			"applications" => [
				[
					"packageName" => $packageName,
					"installType" => "FORCE_INSTALLED",
					"lockTaskAllowed" => true,
					"defaultPermissionPolicy" => "GRANT",
				]
			],
			"passwordPolicies" => [],
			"mountPhysicalMediaDisabled" => true,
			"factoryResetDisabled" => true,
			"permissionGrants" => [
				[
					"permission" => "ENABLE_SYSTEM_APP",
					"policy" => "GRANT"
				]
			],
			"keyguardDisabled"=> true,
			"installUnknownSourcesAllowed" => true,
			"statusReportingSettings" => [
				"applicationReportsEnabled" => true,
				"deviceSettingsEnabled" => true,
				"networkInfoEnabled" => true
			],
			"playStoreMode" => "BLACKLIST",
			"defaultPermissionPolicy" => "GRANT",
			"locationMode" => "LOCATION_ENFORCED",
			"advancedSecurityOverrides" => [
				"untrustedAppsPolicy" => "ALLOW_INSTALL_DEVICE_WIDE"
			],
			"statusBarDisabled" => false,
			"setWallpaperDisabled" => true
		]);

		try {
			// Create or update the policy
			$response = $this->service->enterprises_policies->patch($policyName, $policy);

			return $response;
		} catch (\Google\Service\Exception $e) {
			\Log::error('Google API Error createPolicy: ' . $e->getMessage());
			return $e->getMessage();
		}
	}
	
	public function updateExistingPolicy($policyName, $updatedFields)
	{
		try {
			//$service = $this->initializeClient();
			$policyName = "enterprises/{$this->enterpriseId}/policies/{$policyName}";
			// Retrieve the existing policy
			$existingPolicy = $this->service->enterprises_policies->get($policyName);
			// Merge the updated fields into the existing policy
			foreach ($updatedFields as $key => $value) {
				\Log::error("key".$key);
				/*if ($key === 'applications' && is_array($value)) {
					$existingPolicy->$key = array_merge($existingPolicy->$key, $value); // Merge the existing applications
					$existingPolicy->$key = $value; // Direct assignment for other fields
				} else {
					$existingPolicy->$key = $value; // Direct assignment for other fields
				}*/
				$existingPolicy->$key = $value; 
			}
			//return $existingPolicy;

			// Patch the updated policy
			//$updatedPolicy = $this->service->enterprises_policies->patch($policyName, $existingPolicy);
			 // Update the policy forcefully
			$updatedPolicy = $this->service->enterprises_policies->patch($policyName, $existingPolicy, [
				'updateMask' => implode(',', array_keys($updatedFields)), // Specify fields to update
			]);

			return $updatedPolicy;
		} catch (\Google\Service\Exception $e) {
			\Log::error('Google API Error updateExistingPolicy: ' . $e->getMessage());
			return $e->getMessage();
		}
	}

	
	public function createEnrollmentToken($policyName, $duration = 3600, $userName = null)
    {
        $enterpriseName = "enterprises/{$this->enterpriseId}";
        
        // Configure the enrollment token
        $enrollmentToken = new \Google\Service\AndroidManagement\EnrollmentToken([
            'policyName' => "{$enterpriseName}/policies/{$policyName}",
            'duration' => "{$duration}s",
			/*'allowPersonalUsage' => "PERSONAL_USAGE_DISALLOWED",
			'additionalData' => json_encode([
				'ownership' => 'COMPANY_OWNED'
			]),*/
        ]);

        // Create the token
        return $this->service->enterprises_enrollmentTokens->create($enterpriseName, $enrollmentToken);
    }
	
	public function rebootDevice($deviceId)
	{
		try {
			$deviceName = "enterprises/{$this->enterpriseId}/devices/{$deviceId}";

			// Prepare the reboot command
			$commandBody = new \Google\Service\AndroidManagement\Command([
				'type' => 'REBOOT',
			]);

			// Issue the command
			return $result = $this->service->enterprises_devices->issueCommand($deviceName, $commandBody);
			
		} catch (\Google\Service\Exception $e) {
			\Log::error('rebootDevice Google API error: ' . $e->getMessage());
			return $e->getMessage();
		} catch (\Exception $e) {
			\Log::error('rebootDevice Unexpected error: ' . $e->getMessage());
			return $e->getMessage();
		}
	}
	
	public function changeDevicePassword($deviceId, $newPassword)
	{
		try {
			$deviceName = "enterprises/{$this->enterpriseId}/devices/{$deviceId}";

			// Prepare the password change command
			$commandBody = new \Google\Service\AndroidManagement\Command([
				'type' => 'RESET_PASSWORD',
				'newPassword' => $newPassword,
				"resetPasswordFlags" => [
					"LOCK_NOW"
				]
			]);

			// Issue the command
			return $result = $this->service->enterprises_devices->issueCommand($deviceName, $commandBody);
			
		} catch (\Google\Service\Exception $e) {
			\Log::error('changeDevicePassword Google API error: ' . $e->getMessage());
			return $e->getMessage();
		} catch (\Exception $e) {
			\Log::error('changeDevicePassword Unexpected error: ' . $e->getMessage());
			return $e->getMessage();
		}
	}

	public function deleteDevice($deviceId)
	{
		try {
			$deviceName = "enterprises/{$this->enterpriseId}/devices/{$deviceId}";

			// Delete the device
			$response = $this->service->enterprises_devices->delete($deviceName);

			\Log::info("Device {$deviceId} deleted successfully.");
			return $response;
		} catch (\Google\Service\Exception $e) {
			\Log::error('deleteDevice: Google API error: ' . $e->getMessage());
			return $e->getMessage();
		} catch (\Exception $e) {
			\Log::error('deleteDevice: Unexpected error: ' . $e->getMessage());
			return $e->getMessage();
		}
	}
	
	public function deletePolicy($policyId)
	{
		try {
			$policyName = "enterprises/{$this->enterpriseId}/policies/{$policyId}";

			// Delete the policy
			$response = $this->service->enterprises_policies->delete($policyName);

			\Log::info("Policy {$policyId} deleted successfully.");
			return $response;
		} catch (\Google\Service\Exception $e) {
			\Log::error('deletePolicy: Google API error: ' . $e->getMessage());
			return $e->getMessage();
		} catch (\Exception $e) {
			\Log::error('deletePolicy: Unexpected error: ' . $e->getMessage());
			return $e->getMessage();
		}
	}


}

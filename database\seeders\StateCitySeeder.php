<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class StateCitySeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $states = [
            'Andhra Pradesh', 'Arunachal Pradesh', 'Assam', 'Bihar', 'Chhattisgarh', 'Goa', 'Gujarat',
            'Haryana', 'Himachal Pradesh', 'Jharkhand', 'Karnataka', 'Kerala', 'Madhya Pradesh',
            'Maharashtra', 'Manipur', 'Meghalaya', 'Mizoram', 'Nagaland', 'Odisha', 'Punjab',
            'Rajasthan', 'Sikkim', 'Tamil Nadu', 'Telangana', 'Tripura', 'Uttar Pradesh',
            'Uttarakhand', 'West Bengal'
        ];

        $cities = [
            'Maharashtra' => ['Mumbai', 'Pune', 'Nagpur', 'Nashik'],
            'Karnataka' => ['Bengaluru', 'Mysuru', 'Hubli', 'Mangalore'],
            'Tamil Nadu' => ['Chennai', 'Coimbatore', 'Madurai', 'Salem'],
            'Uttar Pradesh' => ['Lucknow', 'Kanpur', 'Varanasi', 'Agra'],
        ];

        // Insert States
        foreach ($states as $stateName) {
            $stateId = DB::table('states')->insertGetId(['name' => $stateName]);

            // Insert Cities if state exists in cities list
            if (isset($cities[$stateName])) {
                foreach ($cities[$stateName] as $cityName) {
                    DB::table('cities')->insert([
                        'state_id' => $stateId,
                        'name' => $cityName
                    ]);
                }
            }
        }
    }
}

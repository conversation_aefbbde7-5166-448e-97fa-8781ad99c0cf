<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\State;
use App\Models\City;
use App\Models\Country;

class StateCityController extends Controller
{
	// Get all states
    public function getCountries()
    {
        $states = Country::select('id', 'name')->orderby('id')->get();
		
		return response()->json([
            'data' => $states,
        ], 200);
		
        //return response()->json($states);
    }
	
    // Get all states
    public function getStates($country_id)
    {
        $states = State::where('country_id', $country_id)->select('id', 'name')->orderby('id')->get();
        return response()->json($states);
    }

    // Get cities by state ID
    public function getCities($state_id)
    {
        $cities = City::where('state_id', $state_id)->select('id', 'name')->orderby('id')->get();
        return response()->json($cities);
    }
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddPasswordLogsToNewPhoneDeviceInfo extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('new_phone_device_info', function (Blueprint $table) {
            $table->string('default_password', 50)->after('userName')->nullable();
            $table->string('owner_mobile',20)->after('model')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('new_phone_device_info', function (Blueprint $table) {
            $table->dropColumn('default_password');
            $table->dropColumn('owner_mobile');
        });
    }
}

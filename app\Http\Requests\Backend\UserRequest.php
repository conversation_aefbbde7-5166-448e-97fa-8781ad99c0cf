<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Request;

class UserRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(Request $request)
    {
        $rule = 
        [
            'name'          => 'required|max:50',
            // 'email'=> 'required|email',
            'mobile'        => 'required|digits_between:7,15',
            'birth_year'    => 'required|digits:4|integer|min:1900|max:'.(date('Y')),
            'status' => 'required'
        ];
        
        if($request->id){
            $rule['email'] = 'required|email|unique:users,email,' . $request->id;
            if($request->password){
                $rule['password'] = 'string|min:6|confirmed';
                $rule['password_confirmation'] = 'required|string|min:6';
            }
        }
        else{
            $rule['email'] = 'required|email|unique:users,email';
            $rule['password'] = 'string|min:6|confirmed';
            $rule['password_confirmation'] = 'required|string|min:6';
        }
        return $rule;

    }
}

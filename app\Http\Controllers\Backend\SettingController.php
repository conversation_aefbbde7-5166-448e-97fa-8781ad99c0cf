<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Setting;
use App\Models\ApiKey;

class SettingController extends Controller
{
    public function index()
    {
        $settings = Setting::pluck('value', 'key')->toArray();
		$companies = ApiKey::where(['is_active'=>1])->whereNotNull('ip_address')->pluck('company_name','company_name')->toArray();
        return view('backend.settings.index', compact('settings','companies'));
    }

    public function update(Request $request)
    {
        try {
			
            $data = $request->except('_token', 'apk_file', 'lockmaster_apk_link', 'company');

            // Save text-based settings
            foreach ($data as $key => $value) {
                Setting::setValue($key, $value);
            }

            // Handle APK file upload
            if ($request->hasFile('apk_file')) {
                $request->validate([
                    'apk_file' => 'required|max:50000' // Max 50MB
                ]);

                $file = $request->file('apk_file');
                //$fileName = 'app_' . time() . '.' . $file->getClientOriginalExtension();
                $filePath = $file->storeAs('apk', $file->getClientOriginalName(), 'public');

                // Save file path in the database
                Setting::setValue('apk_link', $filePath);
            }
			
			if ($request->hasFile('lockmaster_apk_link')) {
                $request->validate([
                    'lockmaster_apk_link' => 'required|max:50000' // Max 50MB
                ]);

                $file = $request->file('lockmaster_apk_link');
                $filePath = $file->storeAs('customers/apk', $file->getClientOriginalName(), 'public');

                // Save file path in the database
                Setting::setValue('lockmaster_apk_link', $filePath);
            }
            return redirect()->back()->with('alert-success', 'Settings updated successfully!');
        } catch (\Exception $e) {
            return redirect()->back()->with('alert-error', $e->getMessage());
        }
    }
}

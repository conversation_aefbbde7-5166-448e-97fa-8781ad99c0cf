<?php

namespace App\Http\Requests\Backend;


use Illuminate\Foundation\Http\FormRequest;
use \Illuminate\Http\Request;

class DomainWhitelist extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(Request $request)
    {
        $rule = 
        [
            'name' => 'required',
            'company_name' => 'required',
            'ip_address' => 'required',
            'website_url' => 'required',
        ];
		
        /*if($request->segment(3)){
            $rule['email'] = 'required|email|regex:/^([a-z0-9\+_\-]+)(\.[a-z0-9\+_\-]+)*@([a-z0-9\-]+\.)+[a-z]{2,6}$/ix|unique:users,email,' . $request->segment(3);
           
        }
        else{
            $rule['email'] = 'required|email|unique:users,email|regex:/^([a-z0-9\+_\-]+)(\.[a-z0-9\+_\-]+)*@([a-z0-9\-]+\.)+[a-z]{2,6}$/ix';
        }*/
		
        return $rule;
    }
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'username.required' => 'Please enter your mail ',
            'email.required' => 'Please enter your email address',
            'price_licence.required' => 'Please enter your price licence',
            'validity_days.required' => 'Please enter validity days',
            'total_licence.required' => 'Please enter total licence',
        ];
    }
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

}

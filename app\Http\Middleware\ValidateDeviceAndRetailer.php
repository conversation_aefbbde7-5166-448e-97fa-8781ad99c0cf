<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Crypt;
use App\Models\User;

class ValidateDeviceAndRetailer
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        // Validate device_id & retailer_id
        $request->validate([
            'device_id' => 'required|string',
            'retailer_id' => 'required|string'
        ]);

        try {
            // Decrypt retailer_id
            $retailerId = Crypt::decryptString($request->retailer_id);
			
        } catch (\Exception $e) {
            return response()->json(['message' => 'Invalid retailer ID'], 200);
        }

        // Fetch retailer from the database
        //$retailer = User::where('id', $retailerId)->where('role', 'retailer')->first();
		$retailer = User::role('retailer')->where('id', $retailerId)->first();

        if (!$retailer) {
            return response()->json(['message' => 'Retailer not found'], 200);
        }

        // Check if device_id matches the stored one
        if ($retailer->device_id !== $request->device_id) {
            return response()->json(['message' => 'Device ID mismatch'], 200);
        }

        // Add decrypted retailer_id to request for controller access
        $request->merge(['retailer_id' => $retailerId]);

        return $next($request);
    }
}

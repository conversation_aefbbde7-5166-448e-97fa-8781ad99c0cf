<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateKeysLogsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('keys_logs', function (Blueprint $table) {
            $table->id();

			$table->unsignedBigInteger('retailer_id');
			$table->unsignedBigInteger('changed_by');

			$table->enum('action', ['add', 'subtract']);
			$table->integer('quantity');
			$table->integer('total_after_change');
			$table->text('note')->nullable();

			$table->timestamps();

			// Foreign key constraints
			$table->foreign('retailer_id')->references('id')->on('users')->onDelete('cascade');
			$table->foreign('changed_by')->references('id')->on('users')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('keys_logs');
    }
}

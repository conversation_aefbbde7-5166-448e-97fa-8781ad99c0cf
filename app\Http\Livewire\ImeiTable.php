<?php

namespace App\Http\Livewire;

use Livewire\Component;
use Mediconesystems\LivewireDatatables\Http\Livewire\LivewireDatatable;
use Mediconesystems\LivewireDatatables\Column;
use App\Models\User;
use App\Models\DeviceInfo;
use Livewire\WithPagination;

class ImeiTable extends Component
{
	use WithPagination;

    public $search = '';
	public $model = DeviceInfo::class;

    public function updatingSearch()
    {
        $this->resetPage();
    }
	
    public function render()
    {
		$data = DeviceInfo::withTrashed()
			->with([
				'PhoneQr:id,company_id,status,deleted_at',
				'PhoneQr.company:id,name'
			])
			->orderBy('id', 'desc')->paginate(20);
		
        return view('livewire.imei-table', ['data' => $data]);
    }

    public function columns()
    {
        return [
            //Column::name('id')->label('ID')->searchable()->sortable(),
            Column::name('imei')->label('IMEI')->searchable()->sortable(),
            //Column::name('email')->label('Email')->searchable()->sortable(),
            //Column::name('role')->label('Role')->filterable(['admin', 'user']),
            /*Column::callback(['id'], function ($id) {
                return '<a href="/users/edit/'.$id.'" class="btn btn-sm btn-primary">Edit</a>';
            })->label('Actions')->raw()*/
        ];
    }
}

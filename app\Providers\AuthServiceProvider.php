<?php

namespace App\Providers;

use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Gate;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        // 'App\Models\Model' => 'App\Policies\ModelPolicy',
    ];

    /**
     * Register any authentication / authorization services.
     *
     * @return void
     */
    public function boot()
    {
        $this->registerPolicies();

        Gate::define('super-admin-access', function ($user) {
        	return $user->hasRole('super_admin'); // Only Super Admin
    	});

		Gate::define('admin-access', function ($user) {
			return $user->hasAnyRole(['admin', 'national_distributor', 'super_distributor', 'distributor']); // Admin
		});
		
		Gate::define('agent-access', function ($user) {
			return $user->hasRole('agent'); // Agent
		});
		
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class CustomerDeviceSim extends Model
{
    use HasFactory;
	
	use HasFactory, SoftDeletes;

    protected $fillable = ['customer_id', 'mobile_1', 'mobile_2'];

    // Relationship with Customer
    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }
}

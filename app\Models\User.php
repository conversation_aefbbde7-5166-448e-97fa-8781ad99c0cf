<?php

namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use <PERSON>tie\Permission\Traits\HasRoles;
//use Laravel\Passport\HasApiTokens;

// use Kyslik\ColumnSortable\Sortable;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable, HasRoles;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'username','email','password','whatsapp_number','company_name', 'gst_number',
        'is_admin','total_licence','device_type','status','api_token','device_id','role', 'admin_id', 'mpin',
    	'mobile', 'address', 'country', 'state', 'city', 'pincode',
    	'plan', 'fcm_token', 'total_keys', 'used_keys', 'price_per_key', 'total_price', 'retailer_signature'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
    ];

    /**
     * Get the admin who created the retailer.
     */
    public function admin()
    {
        return $this->belongsTo(User::class, 'admin_id')->where('role', 'admin');
    }

    /**
     * Get all retailers under this admin.
     */
    public function retailers()
    {
        return $this->hasMany(User::class, 'admin_id')->where('role', 'retailer');
    }
	
	public function countryDetail()
	{
		return $this->belongsTo(Country::class, 'country');
	}

    // User.php
    public function downlines()
    {
        return $this->hasMany(User::class, 'admin_id', 'id');
    }

	public function stateDetail()
	{
		return $this->belongsTo(State::class, 'state');
	}

	public function cityDetail()
	{
		return $this->belongsTo(City::class, 'city');
	}
	
	/**
     * Get all retailers keys log.
     */
    public function keysLogs()
    {
        return $this->hasMany(KeysLog::class, 'retailer_id');
    }
	
	public function getRemainingKeysAttribute()
    {
        return max($this->total_keys - $this->used_keys, 0);
    }
	
	public function getPlanAttribute($value)
    {
        return ucwords(str_replace('_', ' ', $value));
    }
	
	// Accessor for Active Keys
    public function getActiveKeysAttribute()
    {
        return [
            'fresh' => Customer::where('retailer_id', $this->id)->where('device_status', 'fresh')->count(),
            'installed' => Customer::where('retailer_id', $this->id)->where('device_status', 'installed')->count(),
        ];
    }

    // Accessor for Deleted Keys
    public function getDeletedKeysAttribute()
    {
        return Customer::withTrashed()
			->where('retailer_id', $this->id)
            ->whereIn('device_status', ['formatted', 'uninstalled'])
            ->count();
    }
	
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAppsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('apps', function (Blueprint $table) {
            $table->id();
        	$table->unsignedBigInteger('category_id'); // Foreign key
        	$table->string('application', 150);
        	$table->string('package')->unique();
        	$table->string('icon')->nullable(); // Store icon path
			
			$table->foreign('category_id')->references('id')->on('app_categories')->onDelete('cascade');
			
        	$table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('apps');
    }
}

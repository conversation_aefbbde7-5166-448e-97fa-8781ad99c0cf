<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\ApiKey;
use App\Http\Requests\Backend\DomainWhitelist;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Validator;

class DomainWhitelistController extends Controller
{
    
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $data = Apikey::orderBy('id', 'desc')->paginate(20);        
        return view('backend.domain_whitelist.index', compact('data'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        return view('backend.domain_whitelist.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(DomainWhitelist $request)
    {
        $validator = Validator::make($request->all(), [
			'imei_limit' => [
				'required',
				'numeric',
				'min:1',
                'gt:imei_usage'
			],
		]);

		if ($validator->fails()) {
			return back()->withErrors($validator)->withInput();
		}

        $input = $request->only(['name', 'company_name', 'ip_address', 'website_url', 'imei_limit']);
        $input['key'] = Str::random(32);
        
        Apikey::create($input);
        
        return redirect()->route('admin.domain-whitelist')->with('alert-success', 'Record has been created successfully.');
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $user = Apikey::find($id);
        return view('backend.domain_whitelist.show', compact('user'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $user = Apikey::find($id);
        return view('backend.domain_whitelist.edit', compact('user'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
		$validator = Validator::make($request->all(), [
			'imei_limit' => [
				'required',
				'numeric',
				'min:1',
                'gt:imei_usage'
			],
		]);

		if ($validator->fails()) {
			return back()->withErrors($validator)->withInput();
		}
		
        $user = Apikey::find($id);
        $input = $request->only(['name', 'company_name', 'ip_address', 'website_url', 'imei_limit']);
        $user->update($input);
        
        return redirect()->route('admin.domain-whitelist')->with('alert-success', 'Record updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        User::find($id)->delete();
        return redirect()->route('admin.users.index')
            ->with('alert-success', 'User deleted successfully.');
    }

    /*
     * Update the specified resource in storage.
     * @param  Request $request
     * @return Response
     */
    public function changeStatus(Request $request, $id) {
		
        $user = Apikey::where('id', '=', $id)->first();
		
        if(empty($user)){
            abort(404);
        }
		
        $user->is_active = $request->input('status');
		
        if($user->save()){
            return ['success' => true, 'message' => $request->input('status') == 1 ?'This Record activated successfully.' : "This Record deactivated successfully."];
        }else{
            return ['success' => false, 'message' => 'Your process failed. please try again!!'];
        }
		
    }
}

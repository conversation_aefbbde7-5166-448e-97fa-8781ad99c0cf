<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddImeiUsageToApiKeysTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('api_keys', function (Blueprint $table) {
            $table->integer('imei_limit')->after('website_url')->default(0);  // Maximum allowed IMEIs
    		$table->integer('imei_usage')->after('imei_limit')->default(0);  // Currently used IMEIs
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('api_keys', function (Blueprint $table) {
            $table->dropColumn('imei_limit');
			$table->dropColumn('imei_usage');
        });
    }
}

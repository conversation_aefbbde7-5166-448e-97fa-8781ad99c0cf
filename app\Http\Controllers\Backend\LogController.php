<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Spatie\Activitylog\Models\Activity;

class LogController extends Controller
{
    public function index($imei = null, $company = null)
	{
		$query = Activity::query();

		// Example: Filter logs where properties contain a specific key-value pair
		if ($imei) {
			$query->whereJsonContains('properties->imei', $imei);
		}
		
		if ($company) {
			$query->whereJsonContains('properties->request->client_name', $company);
		}

		$logs = $query->latest()->paginate(10);
		
		//dd($logs);
		
		return view('backend.logs.index', compact('logs'));
	}
}

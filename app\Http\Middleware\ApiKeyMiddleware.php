<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use App\Models\ApiKey;

class ApiKeyMiddleware
{
    public function handle(Request $request, Closure $next)
    {
		//dd(request()->server('SERVER_IP'));
		//$userAgent = $request->header('User-Agent');
		//if (!$userAgent || str_contains($userAgent, 'curl')) {
		//	return response()->json(['message' => 'Unauthorized API access'], 403);
		//}
		
        $apiKey 	= $request->header('x-api-key');
		$clientName = $request->post('client_name');

		$company = Apikey::where(['key'=>$apiKey ,'name'=>$clientName])->where('is_active', true)->first();

		if(!$company){
			return response()->json(['error' => 'Unauthorized'], 401);
		}

        /* if (!$apiKey || !ApiKey::where('key', $apiKey)->where('is_active', true)->exists() || !ApiKey::where('name', $clientName)->where('is_active', true)->exists()) {
            return response()->json(['error' => 'Unauthorized'], 401);
        } */

        return $next($request);
    }
}

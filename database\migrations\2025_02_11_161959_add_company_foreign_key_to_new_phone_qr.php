<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddCompanyForeignKeyToNewPhoneQr extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('new_phone_qr', function (Blueprint $table) {
            $table->unsignedBigInteger('company_id')->after('id')->nullable(); // Adjust data type if needed
        	$table->foreign('company_id')->references('id')->on('api_keys')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('new_phone_qr', function (Blueprint $table) {
            $table->dropForeign(['company_id']);
        	$table->dropColumn('company_id');
        });
    }
}

<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Backend\UserController;
use App\Http\Controllers\Backend\DomainWhitelistController;
use App\Http\Controllers\Backend\PhoneQrController;
use App\Http\Controllers\Backend\SettingController;
use App\Http\Controllers\Backend\LogController;
use App\Http\Controllers\Backend\ImeiController;
use App\Http\Controllers\Backend\AppCategoryController;
use App\Http\Controllers\Backend\AppController;
use App\Http\Controllers\Backend\AdminController;
use App\Http\Controllers\Backend\RetailerController;
use App\Http\Controllers\Backend\AgentController;
use App\Http\Controllers\Backend\AgentDashboardController;
use App\Http\Controllers\Backend\LocationController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::group(['prefix'=>'admin', 'as'=>'admin.'], function () {
	
    //Route::get('/', [App\Http\Controllers\Backend\LoginController::class, 'index'])->name('login');
	
    Route::get('/authentication/login', [App\Http\Controllers\Backend\LoginController::class, 'index'])->name('login');
	Route::get('/get-states/{country_id}', [LocationController::class, 'getStates'])->name('location.states');
	Route::get('/get-cities/{state_id}', [LocationController::class, 'getCities'])->name('location.cities');

   
	Route::group(['middleware' => ['auth', 'auth.superadmin']], function () {

        Route::get('/dashboard', [App\Http\Controllers\Backend\DashboardController::class, 'index'])->name('dashboard');
		
        /*Update Profile Admin */
        Route::get('/profile',[App\Http\Controllers\Backend\DashboardController::class,'profile'])->name('profile');
        Route::post('/update-profile',[App\Http\Controllers\Backend\DashboardController::class,'updateProfile'])->name('updateProfile');

        //recover-password
        Route::get('/reset-password/', [App\Http\Controllers\Backend\DashboardController::class,'getPassword'])->name('reset-password');
        Route::post('/update-password', [App\Http\Controllers\Backend\DashboardController::class,'updatePassword'])->name('updatePassword');

        //Users Route
        Route::get('/users',[App\Http\Controllers\Backend\UserController::class,'index'])->name('users.index');
        Route::get('/users/add',[App\Http\Controllers\Backend\UserController::class,'create'])->name('users.create');
        Route::get('/users/view/{id?}',[App\Http\Controllers\Backend\UserController::class,'show'])->name('users.show');
        Route::post('/users/add/{id?}',[App\Http\Controllers\Backend\UserController::class,'store'])->name('users.store');
		Route::get('/users/edit/{id?}',[UserController::class,'edit'])->name('users.edit');
		Route::patch('/users/update/{id?}',[UserController::class,'update'])->name('users.update');
        Route::post('/users/status/',[App\Http\Controllers\Backend\UserController::class,'status'])->name('users.status');
        Route::post('/users/change-status/{id}',[App\Http\Controllers\Backend\UserController::class,'changeStatus'])->name('users.changeStatus');
		
		Route::post('/agents/update-status/{id}',[AgentController::class,'changeStatus'])->name('agents.update-status');
		Route::resource('agents', AgentController::class);
		
		Route::get('/domain-whitelist',[DomainWhitelistController::class,'index'])->name('domain-whitelist');
        Route::get('/domain-whitelist/add',[DomainWhitelistController::class,'create'])->name('domain-whitelist.create');
        Route::get('/domain-whitelist/view/{id?}',[DomainWhitelistController::class,'show'])->name('domain-whitelist.show');
        Route::post('/domain-whitelist/add',[DomainWhitelistController::class,'store'])->name('domain-whitelist.store');
		Route::get('/domain-whitelist/edit/{id?}',[DomainWhitelistController::class,'edit'])->name('domain-whitelist.edit');
		Route::patch('/domain-whitelist/update/{id?}',[DomainWhitelistController::class,'update'])->name('domain-whitelist.update');
		Route::post('/domain-whitelist/update-status/{id}',[DomainWhitelistController::class,'changeStatus'])->name('domain-whitelist.update-status');
		
		Route::get('app-category/list', [AppCategoryController::class, 'index'])->name('app-category');
		Route::get('app-category/add', [AppCategoryController::class, 'create'])->name('app-category.create');
		Route::post('app-category/add', [AppCategoryController::class, 'store'])->name('app-category.store');
		Route::get('app-category/edit/{id?}', [AppCategoryController::class, 'edit'])->name('app-category.edit');
		Route::patch('app-category/update/{id?}', [AppCategoryController::class, 'update'])->name('app-category.update');
		
		Route::get('app/list', [AppController::class, 'index'])->name('app');
		Route::get('app/add', [AppController::class, 'create'])->name('app.create');
		Route::post('app/add', [AppController::class, 'store'])->name('app.store');
		Route::get('app/edit/{id?}', [AppController::class, 'edit'])->name('app.edit');
		Route::patch('app/update/{id?}', [AppController::class, 'update'])->name('app.update');

        Route::get('/phone-qr',[PhoneQrController::class,'index'])->name('phone-qr');
        Route::post('/phone-qr/add',[PhoneQrController::class,'store'])->name('phone-qr.store');
		
		Route::get('/settings', [SettingController::class, 'index'])->name('settings');
    	Route::post('/settings/update', [SettingController::class, 'update'])->name('settings.update');

    });
	
	Route::group(['middleware' => ['auth', 'auth.agent']], function () {
		
		Route::get('/imei',[ImeiController::class,'index'])->name('imei');
		Route::post('/imei/send-command',[ImeiController::class,'sendCommand'])->name('send-command');
		
		Route::get('/logs/{imei?}/{company?}',[LogController::class,'index'])->name('logs');
		
		// Show change password form
		Route::get('agent/change-password', [AgentDashboardController::class, 'showChangePasswordForm'])->name('agent.change-password.form');

		// Handle password update
		Route::post('agent/change-password', [AgentDashboardController::class, 'updatePassword'])->name('agent.change-password.update');

		
	});
	
	Route::group(['middleware' => ['auth', 'auth.admin']], function () {
		
		Route::get('/admin-dashboard', [AdminController::class, 'index'])->name('admin-dashboard');
		Route::get('/admin-profile',[App\Http\Controllers\Backend\AdminController::class,'profile'])->name('admin-profile');
        Route::post('/admin-update-profile',[App\Http\Controllers\Backend\AdminController::class,'updateProfile'])->name('admin-updateProfile');
		
		Route::post('/retailers/update-status/{id}',[RetailerController::class,'changeStatus'])->name('retailers.update-status');
		Route::get('/retailers/manage-keys/{id}',[RetailerController::class,'manageKeys'])->name('retailers.manage-keys');
		Route::post('/retailers/update-keys/{id}',[RetailerController::class,'updateKeys'])->name('retailers.update-keys');
		Route::get('/retailers/show-keys/{id}',[RetailerController::class,'showKeys'])->name('retailers.show-keys');
		Route::resource('retailers', RetailerController::class);
		
		//Users Route
        Route::get('/users',[App\Http\Controllers\Backend\UserController::class,'index'])->name('users.index');
        Route::get('/users/add',[App\Http\Controllers\Backend\UserController::class,'create'])->name('users.create');
        Route::get('/users/view/{id?}',[App\Http\Controllers\Backend\UserController::class,'show'])->name('users.show');
        Route::post('/users/add/{id?}',[App\Http\Controllers\Backend\UserController::class,'store'])->name('users.store');
		Route::get('/users/edit/{id?}',[UserController::class,'edit'])->name('users.edit');
		Route::patch('/users/update/{id?}',[UserController::class,'update'])->name('users.update');
        Route::post('/users/status/',[App\Http\Controllers\Backend\UserController::class,'status'])->name('users.status');
        Route::post('/users/change-status/{id}',[App\Http\Controllers\Backend\UserController::class,'changeStatus'])->name('users.changeStatus');
		
	});
	
});

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCustomerEmergencyContactsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('customer_emergency_contacts', function (Blueprint $table) {
            $table->id();
			$table->unsignedBigInteger('customer_id');
			$table->string('name', 150);
			$table->string('phone', 20);
			$table->timestamps(); // Use created_at to track insertion order
            $table->foreign('customer_id')->references('id')->on('customers')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('customer_emergency_contacts');
    }
}

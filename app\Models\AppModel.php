<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class AppModel extends Model
{
    use HasFactory;
	
	protected $table = 'apps';
	
    protected $fillable = ['category_id', 'application', 'package', 'icon'];

    public function category()
    {
        return $this->belongsTo(AppCategory::class);
    }
	
	public function getIconAttribute($value)
    {
        return $value ? config('app.url').Storage::url('app-icons/'.$value) : null;
    }
}

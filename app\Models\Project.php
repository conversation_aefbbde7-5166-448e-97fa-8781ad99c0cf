<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Model;

class Project extends Model
{
    protected $table = 'project';
	use SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
       
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        
    ];

     /**
     * Get the user profile associated with the user.
     */
    public function enterprises()
    {
        return $this->hasOne('App\Models\Enterprises','project_id');
    }


     /**
     * Get the user profile associated with the user.
     */
    public function sellerlist()
    {
        return $this->hasMany('App\Models\SellerList','project_id');
    }
}

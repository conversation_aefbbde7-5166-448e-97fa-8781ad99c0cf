/* Users page specific styles */

/* Example: style for user table */
.user-table {
    width: 100%;
    border-collapse: collapse;
}
.user-table th, .user-table td {
    padding: 8px;
    border: 1px solid #ddd;
}
/* Add more styles as needed */

.card-header .card-title {
    color: #495057;
    font-weight: 600;
}

.form-label {
    font-weight: 500;
    color: #495057;
    margin-bottom: 0.5rem;
}

.form-label i {
    color: #6c757d;
    margin-right: 0.5rem;
}

.d-flex.gap-2 > * {
    margin-right: 0.5rem;
}

.d-flex.gap-2 > *:last-child {
    margin-right: 0;
}

#filterSection {
    transition: all 0.3s ease;
}

.btn-group-sm > .btn, .btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

.user-avatar {
    margin-right: 0.5rem !important;
}

.table th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
    color: #495057;
    font-size: 0.875rem;
}

.table th i {
    margin-right: 0.25rem;
    color: #6c757d;
}

.table td {
    vertical-align: middle;
    font-size: 0.875rem;
}

.badge {
    font-size: 0.75rem;
}

.btn-group .btn {
    margin-right: 0.25rem;
}

.btn-group .btn:last-child {
    margin-right: 0;
}

.pagination-info {
    font-size: 0.875rem;
}

.alert-info {
    border-left: 4px solid #17a2b8;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.025);
}

/* Stats Cards */
.card.bg-primary, .card.bg-success, .card.bg-danger, .card.bg-info {
    border: none;
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
}

.card.bg-primary .card-body,
.card.bg-success .card-body,
.card.bg-danger .card-body,
.card.bg-info .card-body {
    padding: 1.25rem;
}

.me-3 {
    margin-right: 1rem !important;
}

/* Custom checkbox styling */
.custom-control-input:checked ~ .custom-control-label::before {
    background-color: #007bff;
    border-color: #007bff;
}

/* Bulk actions button animation */
#bulkActionsBtn {
    transition: all 0.3s ease;
}

/* Table enhancements */
.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

.user-avatar {
    transition: all 0.2s ease;
}

.user-avatar:hover {
    transform: scale(1.1);
}

/* Button group improvements */
.btn-group .btn {
    transition: all 0.2s ease;
}

.btn-group .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.15);
}

/* Status badges */
.badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
}

/* Filter section improvements */
.card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

/* Export modal styling */
#exportSummaryModal .card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

#exportSummaryModal .card.bg-light {
    background-color: #f8f9fa !important;
}

#appliedFilters ul li {
    padding: 0.25rem 0;
    border-bottom: 1px solid #e9ecef;
}

#appliedFilters ul li:last-child {
    border-bottom: none;
}

#appliedFilters ul li i {
    margin-right: 0.5rem;
}

/* Export dropdown improvements */
.dropdown-menu {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    border: none;
}

.dropdown-item {
    padding: 0.75rem 1rem;
    transition: all 0.2s ease;
}

.dropdown-item:hover {
    background-color: #f8f9fa;
    transform: translateX(5px);
}

.dropdown-item i {
    margin-right: 0.75rem;
    width: 1rem;
}

/* Transfer Keys Modal */
#transferKeysModal .card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

#transferKeysModal .card.bg-light {
    background-color: #f8f9fa !important;
}

#transferKeysModal .card-title {
    color: #495057;
    font-weight: 600;
    margin-bottom: 1rem;
}

#transferSummary .alert {
    border-left: 4px solid #17a2b8;
}

#transferSummary ul li {
    padding: 0.25rem 0;
    border-bottom: 1px solid #e9ecef;
}

#transferSummary ul li:last-child {
    border-bottom: none;
}

/* Transfer Keys Button */
.transferKeys {
    transition: all 0.2s ease;
}

.transferKeys:hover {
    transform: scale(1.05);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.15);
}

/* Search highlighting */
mark {
    background-color: #fff3cd;
    padding: 0.1rem 0.2rem;
    border-radius: 0.2rem;
    font-weight: bold;
}

/* Loading states */
.btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

/* Tooltip improvements */
[title] {
    cursor: help;
}

/* Animation for status changes */
.updateStatus {
    transition: all 0.3s ease;
}

.updateStatus:hover {
    transform: scale(1.05);
}

/* Table row selection */
.table tbody tr.selected {
    background-color: rgba(0, 123, 255, 0.1);
}

/* Responsive improvements */
@media (max-width: 576px) {
    .card.bg-primary .card-body h4,
    .card.bg-success .card-body h4,
    .card.bg-danger .card-body h4,
    .card.bg-info .card-body h4 {
        font-size: 1.5rem;
    }

    .table th, .table td {
        font-size: 0.8rem;
        padding: 0.5rem 0.25rem;
    }

    .btn-group .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }
}

@media (max-width: 768px) {
    .btn-group {
        flex-direction: column;
    }

    .btn-group .btn {
        margin-bottom: 0.25rem;
        margin-right: 0;
    }

    .d-flex.gap-2 {
        flex-direction: column;
    }

    .d-flex.gap-2 > * {
        margin-right: 0;
        margin-bottom: 0.5rem;
    }

    .stats-card {
        margin-bottom: 1rem;
    }
}

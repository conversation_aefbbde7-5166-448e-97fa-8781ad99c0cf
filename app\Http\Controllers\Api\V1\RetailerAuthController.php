<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;
use App\Services\FireBaseService;
use App\Models\User;
use App\Models\Setting;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Crypt;

class RetailerAuthController extends Controller
{
	
	protected $firebaseService;
	
    /**
     * Register a new retailer
     */
    public function register(Request $request)
    {
        $validator = Validator::make($request->all(), [
			'name' => 'required|string|alpha_num|max:100',
			'email' => 'required|string|email|max:100|unique:users',
			'password' => 'required|string|min:6',
			'mobile' => 'required|string|max:15|unique:users',
			'admin_email' => 'required|email|exists:users,email',
			'admin_mobile' => 'required|string|exists:users,mobile',
			'address' => 'nullable|string|max:150',
			'country' => 'required|exists:countries,id',
			'state' => 'required|exists:states,id',
			'city' => 'required|exists:cities,id',
			'pincode' => 'required|string|max:8',
			//'plan' => 'required|string',
			'fcm_token' => 'nullable|string',
			'device_id' => 'nullable|string',
			'total_keys' => 'nullable|integer',
			'price_per_key' => 'nullable|numeric',
			'total_price' => 'nullable|numeric',
			'retailer_signature' => 'nullable|string',
    	]);

		if ($validator->fails()) {
			return response()->json([
				'status' => false,
				'message' => 'Validation failed',
				'errors' => $validator->errors(),
			], 200);
		}
		
		// Find admin using email and mobile
		$admin = User::role('distributor')
					->where('email', $request->admin_email)
					->where('mobile', $request->admin_mobile)
					 /* ->where('is_admin', 0) // Ensure it's an admin
					 ->where('role', 'admin')  */
					->first();

		if (!$admin) {
			return response()->json(['error' => 'Admin not found or invalid credentials.'], 200);
		}

        $user = User::create([
            'username' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'is_admin' => 0,
			'admin_id' => $admin->id,
            //'role' => 'retailer',
            'mobile' => $request->mobile,
            'address' => $request->address,
            'country' => $request->country,
            'state' => $request->state,
            'city' => $request->city,
            'pincode' => $request->pincode,
            'plan' => $admin->plan,
            'fcm_token' => $request->fcm_token,
			'device_id' => $request->device_id,
            'total_keys' => $request->total_keys ?? 0,
            'price_per_key' => $request->price_per_key ?? 0,
            'total_price' => $request->total_price ?? 0,
            'retailer_signature' => $request->retailer_signature,
			'status' => 0,
        ]);

		$user->assignRole('retailer');

        $token = $user->createToken('RetailerToken')->plainTextToken;

		$data = [
			'name' => $user->username,
			'email' => $user->email,
			'mobile' => $user->mobile,
			'address' => $user->address,
			'country' => $user->country,
			'state' => $user->state,
			'city' => $user->city,
			'pincode' => $user->pincode,
			'plan' => $user->plan,
			'total_keys' => $user->total_keys,
			'price_per_key' => $user->price_per_key,
			'total_price' => $user->total_price,
			'mpin_status' => $user->mpin ? 'verified' : 'pending',
            'country_detail' => $user->countryDetail,
			'state_detail' => $user->stateDetail,
			'city_detail' => $user->cityDetail,
		];		

        return response()->json([
            'message' => 'Retailer registered successfully!',
            'user' => $data,
            'token' => $token
        ], 200);
    }

    /**
     * Retailer Login
     */
    public function login(Request $request)
    {
        $request->validate([
            'mobile' => 'required|string|max:15',
            'password' => 'required',
			'fcm_token' => 'nullable|string',
			'device_id' => 'required|string',
			'mpin' => 'nullable|string',
        ]);

        $user = User::role('retailer')->where('mobile', $request->mobile)->first();

        if (!$user || !Hash::check($request->password, $user->password)) {
			return response()->json([
				'success' => false,
				'message' => "The provided credentials are incorrect."
			], 200);
        }
		
		if($user->status !== 1) {
			return response()->json([
				'success' => false,
				'message' => "Your account is not active. Please contact support."
			], 200);
		}
		
		// Update MPIN if provided
		if ($request->has('mpin')) {
			$user->mpin = $request->mpin;
		}
		
		if (empty($user->device_id)) {
			$user->device_id = $request->device_id; // Update Device ID if empty
		} else {
			if($user->device_id != $request->device_id) {
				return response()->json([
					'success' => false,
					'message' => "Device Id Mismatch."
				], 200);
			}
		}

		// Update FCM Token if provided
		if ($request->has('fcm_token')) {
			$user->fcm_token = $request->fcm_token;
		}

		$user->save();

        $token = $user->createToken('RetailerToken')->plainTextToken;
		
		$data = [
			'retailer_id' => Crypt::encryptString($user->id),
			'name' => $user->username,
			'email' => $user->email,
			'mobile' => $user->mobile,
			'address' => $user->address,
			'country' => $user->country,
			'state' => $user->state,
			'city' => $user->city,
			'pincode' => $user->pincode,
			'plan' => $user->plan,
			'total_keys' => $user->total_keys,
			'price_per_key' => $user->price_per_key,
			'total_price' => $user->total_price,
			'mpin_status' => $user->mpin ? 'verified' : 'pending',
            'country_detail' => $user->countryDetail,
			'state_detail' => $user->stateDetail,
			'city_detail' => $user->cityDetail,
		];

        return response()->json([
			'success' => true,
            'message' => 'Login successful',
            'user' => $data,
            'token' => $token
        ], 200);
    }
	
	public function logout(Request $request)
	{
		$user = auth()->user();

		// Delete the current access token
		$user->tokens()->delete();

		// Clear FCM token (optional)
		//$user->fcm_token = null;
		$user->save();

		return response()->json(['message' => 'Logout successful'], 200);
	}
	
	/**
     * Fetch Plans
     */
    public function fetchPlans(Request $request)
    {		
		$data = ['Essentials', 'Ultimate', 'Smart Parent'];
		
		return response()->json([
            'data' => $data,
        ], 200);
    }
	
	/**
     * Get Retailer Dashboard
     */
    public function dashboard(Request $request)
    {
		$user = auth()->user();

		$data = [
			'total_keys' => $user->total_keys,
			'price_per_key' => $user->price_per_key,
			'keys_left' => $user->remaining_keys,
			'keys_used' => $user->used_keys,
			'active_keys' => $user->active_keys['fresh'],
			'deleted_keys' => $user->deleted_keys, // Includes "formatted" & "uninstalled"
			//'active_keys' => $user->active_keys,  // Includes "fresh" & "installed"
		];

		return response()->json($data, 200);
    }

    /**
     * Get Retailer Profile
     */
    public function profile(Request $request)
    {
		/*$body 		= 'Message from firebase';
		$title 		= 'Firebase Title';
		$firebase_token = 'dhgB9HFQQAiAHL7bKgok8B:APA91bFaaw-I0RF_qSjobwQDA0rJIEETXAaIPpj08VIg1jf8DaNJUHh4UWvSo0qHuGhizZl5rR6tQBDgUIxmldAzlqBP8nwWhs-WM89jCkP9Qi1aIYL3ZB8';
		//test notification
		$this->firebaseService = new FireBaseService();
		$firebaseResponse = $this->firebaseService->sendPushNotification($firebase_token,$title,$body,'1');
		dd($firebaseResponse);*/
		
		$user =User::where('id', auth()->id())
		->with([
			'keysLogs' => function ($query) {
				$query->select('id', 'retailer_id', 'action', 'quantity', 'note', 'created_at');
			},
			'countryDetail:id,name', 'stateDetail:id,name', 'cityDetail:id,name'
		])
		->first();

		// Fetch the admin details (if the user is a retailer)
		$admin = $user->admin;

		return response()->json([
			//'id' => $user->id,
			'name' => $user->username,
			'email' => $user->email,
			'mobile' => $user->mobile,
			'address' => $user->address,
			'country' => $user->country,
			'state' => $user->state,
			'city' => $user->city,
			'pincode' => $user->pincode,
			'plan' => $user->plan,
			'total_keys' => $user->total_keys,
			'used_keys' => $user->used_keys,
			'left_keys' => $user->remaining_keys,
			'active_keys' => $user->active_keys,  // Includes "fresh" & "installed"
        	'deleted_keys' => $user->deleted_keys, // Includes "formatted" & "uninstalled"
			'price_per_key' => $user->price_per_key,
			'total_price' => $user->total_price,
			'admin_email' => $admin ? $admin->email : null,
			'admin_mobile' => $admin ? $admin->mobile : null,
			'admin_whatsapp' => $admin ? $admin->whatsapp_number : null,
			'mpin_status' => $user->mpin ? 'verified' : 'pending',
			'payment_logs' => $user->keysLogs,
			'country_detail' => $user->countryDetail,
			'state_detail' => $user->stateDetail,
			'city_detail' => $user->cityDetail,
		], 200);
    }
	
	public function updateProfile(Request $request)
    {
        $request->validate([
            'country' => 'nullable',
            'state' => 'nullable',
            'city' => 'nullable',
            'pincode' => 'nullable|string|max:10',
            'retailer_signature' => 'nullable|string|max:255',
        ]);

        // Get retailer
        $retailer = User::with([
						'countryDetail:id,name', 'stateDetail:id,name', 'cityDetail:id,name'
					])
					->find(auth()->user()->id);

        // Update retailer details
        $retailer->update([
            'country' => $request->country ?? $retailer->country,
            'state' => $request->state ?? $retailer->state,
            'city' => $request->city ?? $retailer->city,
            'pincode' => $request->pincode ?? $retailer->pincode,
            //'retailer_signature' => $request->retailer_signature ?? $retailer->retailer_signature,
        ]);

		$data = [
			'name' => $retailer->username,
			'email' => $retailer->email,
			'mobile' => $retailer->mobile,
			'address' => $retailer->address,
			'country' => $retailer->country,
			'state' => $retailer->state,
			'city' => $retailer->city,
			'pincode' => $retailer->pincode,
			'plan' => $retailer->plan,
			'total_keys' => $retailer->total_keys,
			'price_per_key' => $retailer->price_per_key,
			'total_price' => $retailer->total_price,
			'mpin_status' => $retailer->mpin ? 'verified' : 'pending',
            'country_detail' => $retailer->countryDetail,
			'state_detail' => $retailer->stateDetail,
			'city_detail' => $retailer->cityDetail,
		];

        return response()->json([
            'message' => 'Profile updated successfully',
            'user' => $data
        ], 200);
    }
	
	public function verifyMpin(Request $request)
    {
		
		$request->validate([
            'mpin' => 'required|string|min:4|max:6',
        ]);

        // Get the user
		$retailer = auth()->user();
		
		if($retailer->status !== 1) {
			return response()->json([
				'success' => false,
				'message' => "Your account is not active. Please contact support."
			], 200);
		}

        // Check MPIN
		if ($retailer->mpin && $request->mpin !== $retailer->mpin) {
			return response()->json([
				'success' => false,
				'message' => "Invalid MPIN"
			], 200);
		}
		
		//Add if not exist
		$retailer->update(['mpin'=>$request->mpin]);
		
		return response()->json([
			'success' => true,
			'message' => 'MPIN verified successfully',
			//'user' => $retailer
		], 200);
		
    }
	
	public function appVersion(Request $request)
    {		
		$data = [
			'version' => Setting::getValue('lockmaster_app_version'),
			'description' => Setting::getValue('lockmaster_app_updates'),
		];
		
		return response()->json([
            'data' => $data,
        ], 200);
    }
	
}

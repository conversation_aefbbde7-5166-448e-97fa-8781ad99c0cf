<?php

namespace App\Http\Requests\Backend;


use Illuminate\Foundation\Http\FormRequest;
use \Illuminate\Http\Request;

class UpdateLicence extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(Request $request)
    {
        $rule = 
        [
            'price_licence' => 'required',
            'validity_days' => 'required',
        ];
        
        return $rule;
    }
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'validity_days.required' => 'Please enter validity days',
            'total_licence.required' => 'Please enter total licence',
        ];
    }
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

}

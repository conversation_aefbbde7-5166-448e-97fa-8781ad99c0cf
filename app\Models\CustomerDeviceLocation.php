<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class CustomerDeviceLocation extends Model
{
    use HasFactory, SoftDeletes;
	
	protected $fillable = ['customer_id', 'lat', 'long'];

    // Relationship with Customer
    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

}

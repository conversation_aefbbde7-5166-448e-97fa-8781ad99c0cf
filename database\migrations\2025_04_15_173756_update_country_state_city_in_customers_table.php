<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateCountryStateCityInCustomersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
		Schema::table('customers', function (Blueprint $table) {
            $table->dropColumn(['country', 'state', 'city']);
        });
		
        Schema::table('customers', function (Blueprint $table) {			
			$table->unsignedBigInteger('country')->after('address')->nullable();
            $table->unsignedBigInteger('state')->after('country')->nullable();
            $table->unsignedBigInteger('city')->after('state')->nullable();

            // Add foreign keys
            $table->foreign('country')->references('id')->on('countries')->onDelete('set null');
            $table->foreign('state')->references('id')->on('states')->onDelete('set null');
            $table->foreign('city')->references('id')->on('cities')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('customers', function (Blueprint $table) {
            $table->dropForeign(['country']);
            $table->dropForeign(['state']);
            $table->dropForeign(['city']);

            $table->dropColumn(['country', 'state', 'city']);
        });
    }
}

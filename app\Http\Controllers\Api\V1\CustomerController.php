<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Http\Traits\ApiGlobalFunctions;
use Carbon\Carbon;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Storage;
use App\Models\Customer;
use App\Models\User;
use App\Models\NewPhoneQr as PhoneQr;
use App\Models\Setting;
use App\Models\CustomerDeviceLocation;
use App\Models\CustomerDeviceSim;
use App\Models\CustomerDefaultPassword;
use App\Models\AppModel;
use App\Models\AppCategory;
use App\Services\FireBaseService;
use App\Services\ImeiActivityService;

class CustomerController extends Controller
{
    use ApiGlobalFunctions;
	
	public function index(Request $request)
    {
        $query = Customer::select('id', 'name', 'email', 'mobile', 'customer_photo', 'product', 'device_type',
					'imei_1', 'imei_2', 'id_proof', 'customer_sign', 'retailer_sign','device_status', 'reg_date',
					'is_locked'
					)
					->withTrashed()
					//->with('defaultPasswords:id,customer_id,default_password')
					->where('retailer_id', auth()->user()->id);
		
		// Apply search filters
		if ($request->has('search') && !empty($request->search)) {
			$search = $request->search;
			$query->where(function ($q) use ($search) {
				$q->where('name', 'LIKE', "%$search%")
				  ->orWhere('email', 'LIKE', "%$search%")
				  ->orWhere('mobile', 'LIKE', "%$search%")
				  ->orWhere('imei_1', 'LIKE', "%$search%")
				  ->orWhere('imei_2', 'LIKE', "%$search%")
				  ->orWhere('financer_name', 'LIKE', "%$search%");
			});
		}
		
		// Apply field-specific filters
		if ($request->has('name') && !empty($request->name)) {
			$query->where('name', 'LIKE', "%{$request->name}%");
		}

		if ($request->has('email') && !empty($request->email)) {
			$query->where('email', 'LIKE', "%{$request->email}%");
		}

		if ($request->has('mobile') && !empty($request->mobile)) {
			$query->where('mobile', 'LIKE', "%{$request->mobile}%");
		}

		if ($request->has('imei_1') && !empty($request->imei_1)) {
			$query->where('imei_1', 'LIKE', "%{$request->imei_1}%");
		}

		if ($request->has('imei_2') && !empty($request->imei_2)) {
			$query->where('imei_2', 'LIKE', "%{$request->imei_2}%");
		}

		if ($request->has('financer_name') && !empty($request->financer_name)) {
			$query->where('financer_name', 'LIKE', "%{$request->financer_name}%");
		}
		
		if ($request->has('device_status') && !empty($request->device_status)) {
			$query->where('device_status', $request->device_status);
		}
		
		if ($request->has('locked')) {
			$query->where(['is_locked' => $request->locked, 'device_status'=>'installed']);
		}

		// Paginate results (default 10 per page)
		$customers = $query->orderby('id', 'desc')->paginate($request->get('per_page', 10));
		
		foreach ($customers as $customer) {
			$customer->defaultPasswords = CustomerDefaultPassword::select('default_password as value')
										->where('customer_id', Crypt::decryptString($customer->id))->get();
		}

        return response()->json([
            'message' => 'Customers retrieved successfully',
            'customers' => $customers
        ], 200);
    }
	
	// 1️⃣ Add Customer
    public function store(Request $request)
    {
		try {

			$validator = Validator::make($request->all(), [
				'name' => 'required|regex:/^[a-zA-Z0-9 ]+$/|max:100',
				'email' => 'nullable|email|max:100|unique:customers,email',
				'mobile' => 'required|string|max:15|unique:customers,mobile',
				'customer_photo' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
				'id_proof' => 'nullable|mimes:pdf,jpeg,png,jpg|max:2048',
				'customer_sign' => 'nullable|image|mimes:jpeg,png,jpg|max:1024',
				'retailer_sign' => 'nullable|image|mimes:jpeg,png,jpg|max:1024',
				'address' => 'nullable|string|max:255',
				'product' => 'nullable|string|max:100',
				'device_type' => 'nullable|in:phone,tab',
				'imei_1' => 'nullable|string|max:20|unique:customers,imei_1',
				'imei_2' => 'nullable|string|max:20',
				'financer_name' => 'nullable|string|max:100',
				'product_price' => 'nullable|numeric',
				'downpayment' => 'nullable|numeric',
				'balance_amount' => 'nullable|numeric',
				'emi_duration' => 'nullable|in:monthly,15 days,weekly',
				'num_of_months_weeks' => 'nullable|integer',
				'interest_rate' => 'nullable|numeric',
				'total_emi' => 'nullable|numeric',
				'monthly_weekly_emi' => 'nullable|numeric',
				'country' => 'nullable|exists:countries,id',
				'state' => 'nullable|exists:states,id',
				'city' => 'nullable|exists:cities,id',
				'reg_date' => 'nullable|date',
				'firebase_token' => 'nullable'
			],[
    			'name.regex' => 'The Name only contain letters, numbers, and spaces.',
			]);

			if ($validator->fails()) {
				return response()->json(['error' => $validator->errors()], 200);
			}
			
			//dd($request->all());
			
			$retailer = User::find(auth()->user()->id);

			if ($retailer->total_keys <= 0) {
				return response()->json(['error' => 'No available keys for this retailer.'], 200);
			}
			
			$data = $request->only(['name', 'email', 'mobile', 'address', 'product', 'device_type',
						'imei_1', 'imei_2', 'financer_name', 'product_price', 'downpayment', 'balance_amount',
						'emi_duration', 'num_of_months_weeks', 'interest_rate', 'total_emi', 'monthly_weekly_emi',
						'country', 'state', 'city', 'reg_date','firebase_token'
					]);
			
			// File Upload Logic
			if ($request->hasFile('customer_photo')) {
				$data['customer_photo'] = $this->uploadFile($request->file('customer_photo'), 'customers/photo');
			}

			if ($request->hasFile('id_proof')) {
				$data['id_proof'] = $this->uploadFile($request->file('id_proof'), 'customers/id-proof');
			}

			if ($request->hasFile('customer_sign')) {
				$data['customer_sign'] = $this->uploadFile($request->file('customer_sign'), 'customers/customer-sign');
			}

			if ($request->hasFile('retailer_sign')) {
				$data['retailer_sign'] = $this->uploadFile($request->file('retailer_sign'), 'customers/retailer-sign');
			}
			
			$data['retailer_id'] = auth()->user()->id;

			$customer = Customer::create($data);
			
			$retailer->increment('used_keys');

			$activityData = [
				'imei' => $request->imei_1 ?? null,
				'request' => $request->all()
			];
            ImeiActivityService::log('Add Customer', auth()->user(), null, $activityData, 'customer');

			return response()->json([
				'message' => 'Customer added successfully',
				//'customer' => $customer
			], 200);
		} catch (\Exception $e) {
			$activityData = [
				'imei' => $request->imei_1 ?? null,
				'request' => $e->getMessage()
			];
			ImeiActivityService::log('Add Customer Error', auth()->user(), null, $activityData, 'customer');
            return response()->json(['message' => $e->getMessage()], 200);
        }
    }
	
	// 3️⃣ Get Single Customer (Edit)
    public function edit($encryptedId)
    {
		try {
			$id = Crypt::decryptString($encryptedId);
		} catch (\Exception $e) {
			return response()->json(['message' => 'Invalid ID'], 200);
		}
		
        $customer = Customer::with([
							'retailer:id,username,email,mobile',
							'countryDetail:id,name', 'stateDetail:id,name', 'cityDetail:id,name'
						])
						->find($id);

        if (!$customer) {
            return response()->json(['error' => 'Customer not found'], 200);
        }

        return response()->json([
            'message' => 'Customer details retrieved successfully',
            'customer' => $customer
        ], 200);
    }
	
	public function update(Request $request, $encryptedId)
    {
		try {
			$id = Crypt::decryptString($encryptedId);
		} catch (\Exception $e) {
			return response()->json(['message' => 'Invalid ID'], 200);
		}
		
        $customer = Customer::find($id);
		
        if (!$customer) {
            return response()->json(['error' => 'Customer not found'], 200);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|string|max:100',
            'email' => 'nullable|email|unique:customers,email,'.$id,
            'mobile' => 'sometimes|string|max:15|unique:customers,mobile,'.$id,
            'address' => 'nullable|string|max:255',
            'product' => 'nullable|string|max:100',
            'device_type' => 'nullable|in:phone,tab',
            'imei_1' => 'nullable|string|max:20',
            'imei_2' => 'nullable|string|max:20',
            'customer_photo' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
        	'id_proof' => 'nullable|mimes:pdf,jpeg,png,jpg|max:2048',
        	'customer_sign' => 'nullable|image|mimes:jpeg,png,jpg|max:1024',
        	'retailer_sign' => 'nullable|image|mimes:jpeg,png,jpg|max:1024',
            'financer_name' => 'nullable|string|max:100',
            'product_price' => 'nullable|numeric',
            'downpayment' => 'nullable|numeric',
            'balance_amount' => 'nullable|numeric',
            'emi_duration' => 'nullable|in:monthly,15 days,weekly',
            'num_of_months_weeks' => 'nullable|integer',
            'interest_rate' => 'nullable|numeric',
            'total_emi' => 'nullable|numeric',
            'monthly_weekly_emi' => 'nullable|numeric',
            'country' => 'nullable|exists:countries,id',
			'state' => 'nullable|exists:states,id',
			'city' => 'nullable|exists:cities,id',
            'reg_date' => 'nullable|date',
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()], 200);
        }

        $data = $request->only(['name', 'email', 'mobile', 'address', 'product', 'device_type',
                    'imei_1', 'imei_2', 'financer_name', 'product_price', 'downpayment', 'balance_amount',
                    'emi_duration', 'num_of_months_weeks', 'interest_rate', 'total_emi', 'monthly_weekly_emi',
                    'country', 'state', 'city', 'reg_date'
                ]);

        // File Upload Logic
		if ($request->hasFile('customer_photo')) {
            // Delete old file if exists
            if ($customer->customer_photo && Storage::exists($customer->customer_photo)) {
                Storage::delete($customer->customer_photo);
            }
			$data['customer_photo'] = $this->uploadFile($request->file('customer_photo'), 'customers/photo');
		}

		if ($request->hasFile('id_proof')) {
            // Delete old file if exists
            if ($customer->id_proof && Storage::exists($customer->id_proof)) {
                Storage::delete($customer->id_proof);
            }
			$data['id_proof'] = $this->uploadFile($request->file('id_proof'), 'customers/id-proof');
		}

		if ($request->hasFile('customer_sign')) {
            // Delete old file if exists
            if ($customer->customer_sign && Storage::exists($customer->customer_sign)) {
                Storage::delete($customer->customer_sign);
            }
			$data['customer_sign'] = $this->uploadFile($request->file('customer_sign'), 'customers/customer-sign');
		}

		if ($request->hasFile('retailer_sign')) {
            // Delete old file if exists
            if ($customer->retailer_sign && Storage::exists($customer->retailer_sign)) {
                Storage::delete($customer->retailer_sign);
            }
			$data['retailer_sign'] = $this->uploadFile($request->file('retailer_sign'), 'customers/retailer-sign');
		}

        $customer->update($data);

        return response()->json([
            'message' => 'Customer updated successfully',
            //'customer' => $customer
        ], 200);
    }
	
	public function updateFCM(Request $request) 
	{
		try {
			
			$validator = Validator::make($request->all(), [
				'imei_1' => 'required',
				'firebase_token' => 'required|string'
			]);

			if ($validator->fails()) {
				return $this->sendError('Validation Error.', $validator->errors()->first(), '200');
			}
			
			$imei = $request->imei_1;
			
			// ✅ Check in `customers` table
            $customer = Customer::where('imei_1', $imei)
                ->select('id', 'firebase_token')
                ->first();

            if (!$customer) {
                return $this->sendError('Customer does not exist. Please contact admin.', '', 200);
            }
			
			$activityData = [
				'imei' => $request->imei_1,
				'request' => $request->all()
			];
            ImeiActivityService::log('Update FCM', auth()->user(), null, $activityData, 'customer');

			$customer->firebase_token = $request->firebase_token;
			$customer->save();

			return response()->json([
				'status' => 'SUCCESS',
				'message' => 'Firebase token updated successfully'
			]);

		} catch (\Exception $e) {
			$activityData = [
				'imei' => $request->imei_1,
				'request' => $e->getMessage()
			];
            ImeiActivityService::log('Update FCM Error', auth()->user(), null, $activityData, 'customer');
			
			return $this->sendError('Something went wrong. Please contact support.', '', '400');
		}
	}
	
	public function destroy($encryptedId)
	{
		try {
			// Decrypt customer ID
			$customerId = Crypt::decryptString($encryptedId);

			// Find customer
			$customer = Customer::where('id', $customerId)->first();

			if (!$customer) {
				return response()->json(['message' => 'Customer not found'], 200);
			}

			// Allow deletion only if status is 'fresh'
			if ($customer->device_status !== 'fresh') {
				return response()->json(['message' => 'Customer cannot be deleted'], 200);
			}
			
			$retailer = User::find($customer->retailer_id);

			if ($retailer) {
				$retailer->decrement('used_keys');  // Reduce used_keys
			}

			// Forecefully Delete customer
			$customer->forceDelete();

			return response()->json(['message' => 'Customer deleted successfully'], 200);

		} catch (\Exception $e) {
			return response()->json(['message' => $e->getMessage()], 200);
		}
	}
	
	public function fetchQR(Request $request)
    {

        try {

            $validator = Validator::make($request->all(), [
                'customer_id' => 'required',
				'imei_1' => 'required',
				'imei_2' => 'nullable',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => $validator->errors()->first()
                ], 200);
            }

            try {
                $id = Crypt::decryptString($request->customer_id);
            } catch (\Exception $e) {
                return response()->json(['message' => 'Invalid ID'], 200);
            }
            
            $customer = Customer::with('retailer:id,username,email,mobile')->find($id);
    
            if (!$customer) {
                return response()->json(['error' => 'Customer not found'], 200);
            }
			
			$imei = $request->imei_1;
			
			// ✅ Check in `customers` table
            $customer = Customer::where('imei_1', $imei)
                ->select('id', 'firebase_token', 'device_status')
                ->first();

            if (!$customer) {
                return $this->sendError('Customer does not exist. Please contact admin.', '', 200);
            }
			
			if(in_array($customer->device_status, ['installed','uninstalled','formatted'])) {
				return response()->json(['message' => 'Device already exist'], 200);
			}
			
			//get Qrcode which is common for all
			//$qrCode = PhoneQr::first()->image;
			$qrCode = Setting::getValue('lockmaster_qr_code');
			$qrCodeUrl = config('app.url').Storage::url('customers/qr-code/'.$qrCode); //asset('storage/' . $qrCode);
				
			$data = [
				'qrcode' => $qrCodeUrl,
				//'total_limit' => 0,
				//'remaining_usage' => 0,
			];
			
			$activityData = [
				'imei' => $request->imei_1,
				'customer_id' => $id,
				'request' => $request->all()
			];
            ImeiActivityService::log('Fetch QR', auth()->user(), null, $activityData, 'customer');

            return response()->json($data, 200);

        } catch (\Exception $e) {
			$activityData = [
				'imei' => $request->imei_1,
				'customer_id' => $id,
				'request' => $e->getMessage()
			];
			ImeiActivityService::log('Fetch QR Error', auth()->user(), null, $activityData, 'customer');
            return response()->json(['message' => $e->getMessage()], 200);
        }
    }
	
	public function addDevice(Request $request)
    {

        try {

            $validator = Validator::make($request->all(), [
				'imei_1' => 'required',
				'token' => 'required',
				'device_id' => 'required',
				'imei_2' => 'nullable',
				'model' => 'nullable',
				'manufacture' => 'nullable',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => $validator->errors()->first()
                ], 200);
            }
			
			$imei = $request->imei_1;
			
			// ✅ Check in `customers` table
            $customer = Customer::with('retailer:id,username,email,mobile,plan')
				->where('imei_1', $imei)
                ->select('id', 'retailer_id', 'firebase_token', 'device_status')
                ->first();

            if (!$customer) {
                return $this->sendError('Customer does not exist. Please contact admin.', '', 200);
            }
			
			$activityData = [
				'imei' => $imei,
				'request' => $request->all()
			];
			
            ImeiActivityService::log('Add Device', auth()->user(), null, $activityData, 'customer');
			
			if(in_array($customer->device_status, ['installed','uninstalled','formatted'])) {
				return response()->json(['message' => 'Device already exist'], 200);
			}
			
			$uniquePassword = \Str::random(8);
			
			$ownerMobile = Setting::where('key', 'owner_mobile')->value('value');
			$userKeys = Setting::where('key', 'lockmaster_user_keys')->value('value');
			
			$updateData['firebase_token'] = $request->firebase_token;
			$updateData['device_id'] = $request->device_id;
			$updateData['owner_mobile'] = $ownerMobile;
			$updateData['default_password'] = $uniquePassword;
			$updateData['device_status'] = 'installed';
			
			// Update IMEI 2 if provided
			if ($request->has('imei_2')) {
				$updateData['imei_2'] = $request->imei_2;
			}
			// Update Model if provided
			if ($request->has('model')) {
				$updateData['model'] = $request->model;				
			}
			// Update Manufacture if provided
			if ($request->has('manufacture')) {
				$updateData['manufacture'] = $request->manufacture;				
			}
			
			$customer->update($updateData);
			
			$data = [
				'plan' => $customer->retailer->plan,
				'model' => $request->model ?? null,
				'manufacture' => $request->manufacture ?? null,
				'retailer_name' => $customer->retailer->username,
				'retailer_mobile' => $customer->retailer->mobile,
				'owner_mobile' => $ownerMobile,
				'default_password' => $uniquePassword,
				'user_id' => $userKeys,
			];

            return response()->json($data, 200);

        } catch (\Exception $e) {
			$activityData = [
				'imei' => $customer->imei_1,
				'request' => $e->getMessage()
			];
            ImeiActivityService::log('Add Device Error', auth()->user(), null, $activityData, 'customer');
            return response()->json(['message' => $e->getMessage()], 200);
        }
    }
	
	public function fetchDeviceDetails(Request $request)
    {
        set_time_limit(0);

        try {

            $validator = Validator::make($request->all(), [
                'customer_id' => 'required',
                'type' => 'required|in:LOCATION,SIM'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => $validator->errors()->first()
                ], 200);
            }

            $type = $request->type;

            try {
                $id = Crypt::decryptString($request->customer_id);
            } catch (\Exception $e) {
                return response()->json(['message' => 'Invalid ID'], 200);
            }
            
            $customer = Customer::with('retailer:id,username,email,mobile')->find($id);
    
            if (!$customer) {
                return response()->json(['error' => 'Customer not found'], 200);
            }
			
			$activityData = [
				'imei' => $customer->imei_1,
				'customer_id' => $id,
				'request' => $request->all()
			];
            ImeiActivityService::log('Fetch Device', auth()->user(), null, $activityData, 'customer');

			\Log::info("Fetch Device Details: " . json_encode($request->all()) );

            $firebaseToken = $customer->firebase_token;

            // ✅ Set model based on type
            $deviceModel = $type === 'LOCATION' ? new CustomerDeviceLocation() : new CustomerDeviceSim();

            // ✅ Check daily request limit (Max 5)
            if ($deviceModel->where('customer_id', $id)->whereDate('created_at', Carbon::today())->count() >= 5) {
                return $this->sendError('Daily limit of 5 requests exceeded for this IMEI.', '', 200);
            }

            // ✅ Fetch recent device data (within last 5 minutes)
            $data = $this->fetchDeviceData($type, $id);

            if ($data) {
                return response()->json($data);
            }

			\Log::info("Fetch Device Details:". json_encode(['command_1' => $type]));

            // ✅ Send Firebase Command
            $this->firebaseService = new FireBaseService();
            $title = 'Command Request';
            $body = ['command_1' => $type];
            //$firebaseResponse = $this->firebaseService->sendCommand(['command_1' => $type], $firebaseToken, '', '', '1');
            $firebaseResponse = $this->firebaseService->sendPushNotification($firebaseToken,$title,$body,'1','customer',true);
			
            info("firebase response token: ".$firebaseToken." :response: " . json_encode($firebaseResponse));

            if (!$firebaseResponse) {
                return response()->json(['message' => 'Some issue occurred. Please contact admin.'], 200);
            }

            // ✅ Retry fetching data for 10 seconds
            for ($i = 0; $i < 5; $i++) {
                sleep(2);
                $data = $this->fetchDeviceData($type, $id);
                if ($data) {
                    return response()->json($data);
                }
            }

            return $this->sendResponse([], 'Your submission was successful. Kindly review the history section.');

        } catch (\Exception $e) {
			$activityData = [
				'imei' => $customer->imei_1,
				'customer_id' => $id,
				'request' => $request->all()
			];
            ImeiActivityService::log('Fetch Device Error', $e->getMessage(), null, $activityData, 'customer');
            return response()->json(['message' => $e->getMessage()], 200);
        }
    }

    private function fetchDeviceData($type, $customerId)
    {
        $model = $type === 'LOCATION' ? CustomerDeviceLocation::class : CustomerDeviceSim::class;
        $deviceData = $model::where('customer_id', $customerId)
            ->where('updated_at', '>=', Carbon::now()->subMinutes(5))
            ->first();

        if (!$deviceData) {
            return false;
        }

        return $type === 'LOCATION'
            ? [
                'status' => 'SUCCESS',
                'latitude' => $deviceData->lat,
                'longitude' => $deviceData->long,
                'datetime' => $deviceData->created_at
            ]
            : [
                'status' => 'SUCCESS',
                'mobile1' => $deviceData->mobile_1,
                'mobile2' => $deviceData->mobile_2,
				'datetime' => $deviceData->created_at
            ];
    }
	
	public function handleDeviceAction(Request $request) 
	{
		try {
			
		// Define validation rules based on the event type
		$rules = [
			'customer_id' => 'required',
			'event' => 'required|string|in:format,disableformat,lock,usbenable,cameraenable,uninstall,reboot,suspend,unsuspend,defaultpass,changepassword,wallpaperdisable,wallpaperenable,retailermobile,ownermobile,systemapp,hideapp,changesim',
			'value' => 'nullable'
		];

		$validator = Validator::make($request->all(), $rules);

		if ($validator->fails()) {
			return response()->json([
				'success' => false,
				'message' => $validator->errors()->first()
			], 200);
		}
			
		\Log::info("Sent Firebase Command: " . json_encode($request->all()) );
		
		try {
			$id = Crypt::decryptString($request->customer_id);
		} catch (\Exception $e) {
			return response()->json(['message' => 'Invalid ID'], 200);
		}
		
		$customer = Customer::with('retailer:id,username,email,mobile')->find($id);

        if (!$customer) {
            return response()->json(['error' => 'Customer not found'], 200);
        }

		$event = $request->event;
		$value = $request->value;
		
		//echo $event.''.$value;die;
		
		$title = 'NOTIFY';
		$body = 'You have a new update!';
		$commands = [];
		
		if (in_array($event, ['retailermobile', 'ownermobile']) && empty($value)) {
			return response()->json([
				'success' => false,
				'message' => "The value field is required for $event and must be a number."
			], 200);
		}
		
		if (in_array($event, ['suspend','systemapp','hideapp']) && empty($value)) {
			return response()->json([
				'success' => false,
				'message' => "The value field is required for $event and must be a package."
			], 200);
		}
		
		if (in_array($event, ['lock', 'cameraenable','usbenable']) && empty($value)) {
			return response()->json([
				'success' => false,
				'message' => "The value field is required for $event and must be either 'true' or 'false'."
			], 200);
		}

		/* if ($event === 'wallpaperdisable' && empty($value)) {
			return response()->json([
				'success' => false,
				'message' => "The value field is required for wallpaperdisable and must be a file."
			], 200);
		} */
			
		if (in_array($event, ['defaultpass']) && empty($value)) {
			return response()->json([
				'success' => false,
				'message' => "The value field is required for $event and must be password."
			], 200);
		}
		
		$activityData = [
			'imei' => $customer->imei_1,
			'customer_id' => $id,
			'request' => $request->all()
		];
		ImeiActivityService::log('Send Command', auth()->user(), null, $activityData, 'customer');

		// Process events
		switch ($event) {
			case 'reboot':
				$commands['command_1'] = 'REBOOT';
				break;
			case 'format':
				$commands['command_1'] = 'FORMAT';
				$customer->update(['device_status' => 'formatted' ]);
				$customer->delete();
				break;
			case 'disableformat':
				$commands['command_1'] = 'DISABLEFORMAT';
				$customer->update(['is_disable_format' => 1]);
				break;
			case 'lock':
				$commands['command_1'] = $value === 'true' ? 'LOCK' : 'UNLOCK';
				$customer->update(['is_locked' => $value === 'true' ? 1 : 0 ]);
				break;
			case 'usbenable':
				$commands['command_1'] = $value === 'true' ? 'USBENABLE' : 'USBDISABLE';
				$customer->update(['is_usb_enabled' => $value === 'true' ? 1 : 0 ]);
				break;
			case 'cameraenable':
				$commands['command_1'] = $value === 'true' ? 'CAMERAENABLE' : 'CAMERADISABLE';
				$customer->update(['is_camera_enabled' => $value === 'true' ? 1 : 0 ]);
				break;
			case 'uninstall':
				$commands['command_1'] = 'UNINSTALL';
				$customer->update(['device_status' => 'uninstalled' ]);
				$customer->delete();
				break;
			case 'suspend':
				$commands['command_1'] = 'SUSPEND';
				$commands['packages'] = $value;
				$customer->update(['suspend_apps' => $value]);
				break;
			case 'systemapp':
				$commands['command_1'] = 'SystemApp';
				$commands['packages'] = $value;
				$customer->update(['system_apps' => $value]);
				break;
			case 'hideapp':
				$commands['command_1'] = 'HideApp';
				$commands['packages'] = $value;
				$customer->update(['hide_apps' => $value]);
				break;
			case 'defaultpass':
				$commands['command_1'] = 'DEFAULTPASS';
				$unique_password = $value;
				$commands['password'] = $unique_password;
				$customer->update(['default_password' => $unique_password]);
				CustomerDefaultPassword::create([
					'customer_id' => $id,
					'default_password' => $unique_password
				]);
				break;
			case 'changepassword':				
				$commands['command_1'] = 'CHANGEPASSWORD';
				$commands['password'] = $value;
				break;
			case 'retailermobile':
				$commands['command_1'] = 'RETIAILERNO';
				$commands['mobile'] = $value;
				$customer->update(['retailer_mobile'=>$value]);
				break;
			case 'ownermobile':
				$commands['command_1'] = 'OWNERNO';
				$commands['mobile'] = $value;
				$customer->update(['owner_mobile'=>$value]);
				break;
			case 'changesim':
				
				$commands['command_1'] = 'CHANGESIM';
				$commands['mobile'] = $value;
				$customer->update(['secondary_contacts'=>$value]);
				
				break;
			case 'wallpaperenable':
				$commands['command_1'] = 'WALLPAPERENABLE';
				$customer->update(['is_wallpaper_enabled' => true]);
				break;
			case 'wallpaperdisable':
				/* if ($request->hasFile('value')) {
					$file = $this->uploadFile($request->file('value'), 'customers/wallpapers');
					$filepath = config('app.url').Storage::url('customers/wallpapers/'.$file);
				} else {
					return response()->json([
						'success' => false,
						'message' => "Invalid File"
					], 200);
				} */
				$file = $customer->retailer->plan == 'Smart Parent' ? Setting::getValue('wallpaper_premium') : Setting::getValue('wallpaper_standard');
				$filepath = config('app.url').Storage::url('customers/wallpapers/'.$file);
				$commands['command_1'] = 'WALLPAPERDISABLE';
				$commands['imagePath'] = $filepath;
				$customer->update(['is_wallpaper_enabled' => false]);
				break;
			default:
				return response()->json([
					'success' => false,
					'message' => "Invalid event"
				], 200);
		}
		
		if(isset($commands)){
			\Log::info("firebase response commands: ". json_encode($commands));
			$body = $commands;
			$firebase_token = $customer->firebase_token;
			$this->firebaseService = new FireBaseService();
			$firebaseResponse = $this->firebaseService->sendPushNotification($firebase_token,$title,$body,'1','customer',true);
			\Log::info("firebase response token: ".$firebase_token." :response: " . json_encode($firebaseResponse));
		}

		$data = [
			'status' => 'SUCCESS',
			//'commands' => $commands
		];

		return response()->json($data);
			
		} catch (\Exception $e) {
			$activityData = [
				'imei' => $customer->imei_1,
				'customer_id' => $id,
				'request' => $request->all()
			];
            ImeiActivityService::log('Send Command Error', $e->getMessage(), null, $activityData, 'customer');
            return response()->json(['message' => $e->getMessage()], 200);
        }
	}
	
	public function sendNotification(Request $request) 
	{

		try {
			$validator = Validator::make($request->all(), [
				'customer_id' => 'required',
				'type' => 'required',
				'title' => 'required',
				'message' => 'required'
			]);

			if ($validator->fails()) {
				return response()->json([
					'success' => false,
					'message' => $validator->errors()->first()
				], 200);
			}

			try {
				$id = Crypt::decryptString($request->customer_id);
			} catch (\Exception $e) {
				return response()->json(['message' => 'Invalid ID'], 200);
			}

			$type = $request->type;
			$body = $request->message;
			$title = $request->title;

			// Fetch customer details
			$customer = Customer::where('id', $id)
				->select('firebase_token')
				->first();

			if (!$customer || empty($customer->firebase_token)) {
				return response()->json(['message' => 'Customer not found or does not have a valid Firebase token.'], 200);
			}
			
			$activityData = [
				'customer_id' => $id,
				'request' => $request->all()
			];
            ImeiActivityService::log('Send Notification', auth()->user(), null, $activityData, 'customer');

			// Send notification
			$this->firebaseService = new FireBaseService();
			$firebaseResponse = $this->firebaseService->sendPushNotification($customer->firebase_token,$title,$body,'1','customer');

			if ($firebaseResponse) {
				return response()->json([
					'status' => 'SUCCESS',
					'message' => 'Notification sent successfully'
				]);
			} else {
				return response()->json(['message' => 'Failed to send notification. Please try again.'], 200);
			}
		} catch (\Exception $e) {
			$activityData = [
				'customer_id' => $id,
				'request' => $e->getMessage()
			];
            ImeiActivityService::log('Send Notification Error', auth()->user(), null, $activityData, 'customer');
			return response()->json(['message' => 'Something went wrong. Please contact support.'], 200);
		}
	}
	
	// Get all categories
    public function getCategories()
    {
        $categories = AppCategory::select('id', 'name')->get();

        return response()->json([
            'status' => true,
            'message' => 'Categories fetched successfully.',
            'data' => $categories,
        ]);
    }

    // Get all apps under a specific category
    public function getAppsByCategory($id)
    {
        $apps = AppModel::where('category_id', $id)
            ->select('id', 'application', 'package', 'icon')
            ->get();

        return response()->json([
            'status' => true,
            'message' => 'Apps fetched successfully.',
            'data' => $apps,
        ]);
    }

	public function checkNetwork(Request $request) 
	{
		try {
			
			$validator = Validator::make($request->all(), [
				'imei_1' => 'required',
				'is_connected' => 'required|boolean',
			]);

			if ($validator->fails()) {
				return $this->sendError('Validation Error.', $validator->errors()->first(), '200');
			}
			
			$imei = $request->imei_1;
			
			// ✅ Check in `customers` table
            $customer = Customer::where('imei_1', $imei)
                ->select('id', 'is_connected')
                ->first();

            if (!$customer) {
                return $this->sendError('Customer does not exist. Please contact admin.', '', 200);
            }
			
			$activityData = [
				'imei' => $request->imei_1,
				'request' => $request->all()
			];
            ImeiActivityService::log('Check Internet Connection', auth()->user(), null, $activityData, 'customer');

			$customer->is_connected = $request->is_connected;
			$customer->save();

			return response()->json([
				'status' => 'SUCCESS',
				'message' => 'Setting updated successfully'
			]);

		} catch (\Exception $e) {
			$activityData = [
				'imei' => $request->imei_1,
				'request' => $e->getMessage()
			];
            ImeiActivityService::log('Check Internet Connection Error', auth()->user(), null, $activityData, 'customer');
			
			return $this->sendError('Something went wrong. Please contact support.', '', '400');
		}
	}
	
	/**
	 * Uploads a file with a timestamp-based name
	 */
	private function uploadFile($file, $path)
	{
		$fileName = md5($file->getFilename() . time()) . '.' . $file->getClientOriginalExtension();
		$file->storeAs($path, $fileName, 'public');
		
		return $fileName;
	}
}

<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use Spatie\Permission\Models\Role;

class MigrateUserRoles extends Command
{
    protected $signature = 'roles:migrate-users';
    protected $description = 'Migrate users from old role columns to Spatie roles';

    public function handle()
    {
        $this->info('Starting user role migration...');

        User::withTrashed()->get()->each(function ($user) {
            if ($user->is_admin) {
                $user->assignRole('super_admin');
            } elseif ($user->role === 'admin') {
                $user->assignRole('admin');
            } elseif ($user->role === 'agent') {
                $user->assignRole('agent');
            } elseif ($user->role === 'retailer') {
                $user->assignRole('retailer');
            } else {
                $this->warn("User ID {$user->id} has unknown role: {$user->role}");
            }
        });

        $this->info('✅ User role migration completed.');
    }
}

<?php

namespace App\Http\Controllers\Api\V1;

use App\Models\DeviceInfo;
use App\Models\PhoneDeviceLocation;
use App\Models\PhoneDeviceSim;
use App\Models\Setting;
use App\Models\ApiKey;
use App\Models\NewPhoneQr as PhoneQr;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use App\Http\Controllers\Controller;
use App\Http\Traits\ApiGlobalFunctions;
use DB;
use Validator;
use Illuminate\Support\Facades\Log;
use App\Services\ImeiActivityService;
use Carbon\Carbon;

class DeviceManagementController extends Controller {

    use ApiGlobalFunctions;

	protected $androidService;

    public function __construct()
    {
    }
	
	public function addDevice(Request $request)
    {
		\Log::info('DeviceManagementV2Controller Requests: '. json_encode($request->all()));
		
		$input = $request->all();
		
        $data = [];
		
        try {
            $validator = Validator::make($input, ['imei' => 'required','token' => 'required','device_id' => 'required']);
            if ($validator->fails()) 
            {
                return $this->sendError('Validation Error.', $validator->errors()->first(), '400');
            } else {

				$targetImei = isset($request->imei) ? $request->imei : '';
				$mobile 	= isset($request->mobile) ? $request->mobile : '';
				$deviceId 	= isset($request->device_id) ? $request->device_id : '';
				$ftoken 	= isset($request->token) ? $request->token : '';
				$model 		= isset($request->model) ? $request->model : '';
				$manufacture = isset($request->manufacture) ? $request->manufacture : '';
                $userName = '';
                $ownerMobile = Setting::where('key', 'owner_mobile')->value('value');
                $status = 'SUCCESS';
				$defaultPassword = '';
				
				$sessionId = bin2hex(random_bytes(16)); // 32-character hexadecimal string

				$phoneQr = PhoneQr::where('imei', $targetImei)->first();

				if($phoneQr) {				
					
					if($phoneQr->status == 'used') {
						return response()->json(['error' => 'IMEI already used'], 400);
					}

					$device = DeviceInfo::where('imei', $targetImei)->first();

					if(!$device){

                        $defaultPassword = \Str::random(8);

                        //Retailer API
                        $imeiResponse = $this->fetchImeiDetails($targetImei);
                        if ($imeiResponse->successful()) {
                            // Decode the JSON response
                            $responseData = $imeiResponse->json();

                            if($responseData['data'] == 'Customer not found.') { 
                                $status = 'Customer not found';
                            } else {
                                $mobile = $responseData['data']['RetailerMobile'];
                                $userName = $responseData['data']['RetailerName'];
                            }
                            
                        } else {
                            
                        }

						$deviceModel = new DeviceInfo;
						$deviceModel->phone_qr_id = $phoneQr->id;
						$deviceModel->imei = isset($targetImei ) ? $targetImei  : NULL;
						$deviceModel->mobile = isset($mobile) ? $mobile : NULL;
                        $deviceModel->userName = $userName;
                        $deviceModel->owner_mobile = $ownerMobile;
                        $deviceModel->default_password = $defaultPassword;
						$deviceModel->model = isset($model) ? $model : NULL;
						$deviceModel->device_id = isset($deviceId ) ? $deviceId  : NULL;
						$deviceModel->manufacture = isset($manufacture ) ? $manufacture  : NULL;
						$deviceModel->firebase_token = isset($ftoken ) ? $ftoken  : NULL;
						$deviceModel->session_id = isset($sessionId ) ? $sessionId  : NULL;
						$deviceModel->save();

						$phoneQr->status = 'used';
						$phoneQr->save();
					}

				} else {
					return response()->json(['error' => 'IMEI Not Exist'], 400);
				}
				
				$data = [
                    'status' => $status,
					'session_id' => $sessionId,
                    'model' => $model,
                    'retailer_name' => $userName,
					'retailer_mobile' => $mobile,
					'owner_mobile' => $ownerMobile,
                    'retailer_name' => $userName,
                    'default_password' => $defaultPassword
				];
				return response()->json($data);
            }
        } catch (\Exception $e) {
			\Log::info('Exception: '.$e->getMessage());
            return $this->sendError($this->messageDefault('oops'), '', '400');
        }
		
        return response()->json($msg);
    }

	public function updateToken(Request $request)
    {
		\Log::info('fetchLocation Requests: '. json_encode($request->all()));

		$input = $request->all();

        $data = [];

        try {

            $validator = Validator::make($input, ['imei' => 'required','token' => 'required']);

            if ($validator->fails()) 
            {
                return $this->sendError('Validation Error.', $validator->errors()->first(), '200');
            } else {

				$targetImei = isset($request->imei) ? $request->imei : NULL;
				$ftoken 	= $request->token;

				$device 	= DeviceInfo::where('imei', $targetImei)->first();

				if(!$device){		
					\Log::info('fetchLocationv2 Error: Invalid Id. Please contact admin.');
					return $this->sendError('Invalid IMEI. Please contact admin.', '', '400');
				}

				$deviceId = $device->id;
				\Log::info('fetchLocationv2 deviceId: '.$deviceId);

				$device->firebase_token =isset($ftoken ) ? $ftoken  : NULL;
				$device->save();
				
				$data = [
					'status' => 'SUCCESS'
				];
				return response()->json($data);
            }
        } catch (\Exception $e) {
			\Log::info('fetchLocationv2 Exception: '.$e->getMessage());
            return $this->sendError($this->messageDefault('oops'), '', '400');
        }
		
        return response()->json($msg);
    }
	
	public function fetchLocation(Request $request)
    {
		\Log::info('fetchLocation Requests: '. json_encode($request->all()));

		$input = $request->all();
		$company = ApiKey::where(['company_name'=>$request->client_name, 'key'=>$request->header('x-api-key')])->first();
        $data = [];

        try {

            $validator = Validator::make($input, ['imei' => 'required','long' => 'required','lat' => 'required','datetime' => 'required']);

            if ($validator->fails()) 
            {
                return $this->sendError('Validation Error.', $validator->errors()->first(), '200');
            } else {

				$targetImei = isset($request->imei) ? $request->imei : NULL;
				$long 		= isset($request->long) ? $request->long : NULL;
				$lat 		= isset($request->lat) ? $request->lat : NULL;
				$datetime 	= isset($request->datetime) ? $request->datetime : NULL;

				$device 	= DeviceInfo::where('imei', $targetImei)->first();

				if(!$device){
					return $this->sendError('Invalid IMEI. Please contact admin.', '', '400');
				}

				$deviceId = $device->id;
				
				// Check daily request count
				$todayCount = PhoneDeviceLocation::where('device_id', $deviceId)
					->whereDate('created_at', Carbon::today())
					->count();

				if ($todayCount >= 5) {
					$data = [
						'status' => 'SUCCESS'
					];
					return response()->json($data);
				}

				$deviceLocations = new PhoneDeviceLocation;
				$deviceLocations->device_id=isset($deviceId) ? $deviceId : NULL;
				$deviceLocations->lat = $lat;
				$deviceLocations->long = $long;
				$deviceLocations->datetime = $datetime;
				$deviceLocations->save();				
				
				$data = [
					'status' => 'SUCCESS'
				];
				
				$activityData = [
                    'imei' => $targetImei,
                    'request' => $request->all()
                ];
                ImeiActivityService::log('Fetch Location', $company, null, $activityData);
				
				return response()->json($data);
            }
        } catch (\Exception $e) {
			
			\Log::info('fetchLocationv2 Exception: '.$e->getMessage());
			$activityData = [
				'imei' => $request->imei,
				'request' => $e->getMessage()
			];
			ImeiActivityService::log('Fetch Location',$company, null, $activityData);
			
            return $this->sendError($this->messageDefault('oops'), '', '400');
        }
		
        return response()->json($msg);
    }

	public function fetchSim(Request $request)
    {
		\Log::info('fetchSimv2 Requests: '. json_encode($request->all()));

		$input = $request->all();
		
		$company = ApiKey::where(['company_name'=>$request->client_name, 'key'=>$request->header('x-api-key')])->first();
		
        $data = [];

        try {
			
            $validator = Validator::make($input, ['imei' => 'required','mobile1' => 'required','mobile2' => 'required']);
            
            if ($validator->fails()) 
            {
                return $this->sendError('Validation Error.', $validator->errors()->first(), '200');
            } else {

				$targetImei = isset($request->imei) ? $request->imei : NULL;
				$mobile1 		= isset($request->mobile1) ? $request->mobile1 : NULL;
				$mobile2 		= isset($request->mobile2) ? $request->mobile2 : NULL;
				$device 	= DeviceInfo::where('imei', $targetImei)->first();

				if(!$device){
					return $this->sendError('Invalid IMEI. Please contact admin.', '', '400');
				}
				
				$deviceId = $device->id;

				// Check daily request count
				$todayCount = PhoneDeviceSim::where('device_id', $deviceId)
					->whereDate('created_at', Carbon::today())
					->count();

				if ($todayCount >= 5) {
					$data = [
						'status' => 'SUCCESS'
					];
					return response()->json($data);
				}

				$deviceSim = new PhoneDeviceSim;
				$deviceSim->device_id=isset($deviceId) ? $deviceId : NULL;
				$deviceSim->mobile1 = $mobile1;
				$deviceSim->mobile2 = $mobile2;
				$deviceSim->save();
				
				$activityData = [
                    'imei' => $targetImei,
                    'request' => $request->all()
                ];
                ImeiActivityService::log('Fetch Sim', $company, null, $activityData);
				
				$data = [
					'status' => 'SUCCESS'
				];
				return response()->json($data);
            }
        } catch (\Exception $e) {
			\Log::info('fetchLocationv2 Exception: '.$e->getMessage());
			$activityData = [
				'imei' => $request->imei,
				'request' => $e->getMessage()
			];
			ImeiActivityService::log('Fetch Sim',$company, null, $activityData);
            return $this->sendError($this->messageDefault('oops'), '', '400');
        }
		
        return response()->json($msg);
    }
	
	public function locationSimList(Request $request) 
    {
        $input = $request->all();

        try {

            $validator = Validator::make($input, ['imei' => 'required','type' => 'required']);

            if ($validator->fails()) {
                return $this->sendError('Validation Error.', $validator->errors()->first(), '200');
            } else {

				$type 		= $input['type'];
				$imei 		= $input['imei'];

				$deviceExist = DeviceInfo::where('imei', $imei)->select('id','user_id')->first();
                
				if ($deviceExist) 
				{
					$deviceId		= $deviceExist->id;

					if(is_null($deviceId)){
						return response()->json(['error' => 'Please check IMEI again'], 400);
					}

					$data = [];

					if($type == 'LOCATION') {
						$data = PhoneDeviceLocation::where('device_id',$deviceId)->whereDate('created_at', Carbon::today())->get();
					} else {
						$data = PhoneDeviceSim::where('device_id',$deviceId)->whereDate('created_at', Carbon::today())->get();
					}
					
					return response()->json([
						'status' => 'SUCCESS',
						'data' => $data
					]);
					
				} else {
					return $this->sendError('Device not exist. Please contact admin.', '', '200');
				}                    
                
            }
        } catch (\Exception $e) {
            return $this->sendError($this->messageDefault('oops'), '', '400');
        }
    }
	
	public function getDeviceIdByImei($devices, $targetImei) {
		foreach ($devices as $device) {
			if (isset($device['networkInfo']['imei']) && $device['networkInfo']['imei'] === $targetImei) {
				return $device;
			}
		}
		return null; // Return null if no device with the given IMEI is found
	}
	
	public function fetchDevice(Request $request)
    {
		\Log::info('fetchDevicev2 Requests: '. json_encode($request->all()));

		$input = $request->all();
        $data = [];

        try {
            $validator = Validator::make($input, ['imei' => 'required']);
            if ($validator->fails()) 
            {
                return $this->sendError('Validation Error.', $validator->errors()->first(), '200');
            } else {

				$targetImei = isset($request->imei) ? $request->imei : NULL;
				$device 	= DeviceInfo::where('imei', $targetImei)->first();

				if(!$device){		
					\Log::info('fetchDevicev2 Error: Invalid Id. Please contact admin.');
					return $this->sendError('Invalid IMEI. Please contact admin.', '', '400');
				}
				$deviceId = $device->id;

				\Log::info('fetchDevicev2 deviceId: '.$deviceId);

				$data = [
					'status' => 'SUCCESS',
					'imei' => $request->imei,
					'model' => $device->model,
					'manufacture' => $device->manufacture,
					'mobile' => $device->mobile,
					'device_id' => $device->device_id,
					'purchase_id' => $device->purchase_id
				];
				return response()->json($data);
            }
        } catch (\Exception $e) {
			\Log::info('fetchLocation Exception: '.$e->getMessage());
            return $this->sendError($this->messageDefault('oops'), '', '400');
        }
		
        return response()->json($msg);
    }

    public function fetchPassword(Request $request)
    {

        try {
            $validator = Validator::make($request->all(), ['imei' => 'required']);

            if ($validator->fails()) 
            {
                return $this->sendError('Validation Error.', $validator->errors()->first(), '200');
            } else {

				$targetImei = isset($request->imei) ? $request->imei : NULL;
				$device = DeviceInfo::where('imei', $targetImei)->first();

				if(!$device){
					return $this->sendError('Invalid IMEI. Please contact admin.', '', '400');
				}

				$data = [
					'status' => 'SUCCESS',
					'default_password' => $device->default_password
				];

				return response()->json($data);
            }
        } catch (\Exception $e) {
            return $this->sendError($this->messageDefault('oops'), '', '400');
        }
    }
	
	public function extractName($url){
		$parts = explode('/', $url);
		return $lastWord = end($parts); 
	}

    private function fetchImeiDetails($imei)
    {
        // Optional: Add headers (e.g., for authentication)
        $headers = [
            'x-api-key' => 'ac56bc31aadd82a63a9970ff166313ba5ec37bb6',
            //'Accept' => 'application/json',
        ];

        // Define the multipart data
        $multipartData = [
            [
                'name' => 'imei',
                'contents' => $imei,
            ],
        ];

        // Make the POST request
        $response = Http::withHeaders($headers)
            ->asMultipart()
            ->post('https://emi.paisacops.com/api/multiact/PairSuccess2', $multipartData);
		
		return $response;
    }
	
}
?>
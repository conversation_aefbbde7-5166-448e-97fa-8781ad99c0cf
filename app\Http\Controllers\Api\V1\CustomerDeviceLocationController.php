<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\CustomerDeviceLocation;
use App\Models\Customer;
use Illuminate\Support\Facades\Crypt;
use App\Services\ImeiActivityService;
use Carbon\Carbon;

class CustomerDeviceLocationController extends Controller
{
    // Store location
    public function store(Request $request)
    {
        $request->validate([
            'imei_1' => 'required',
            'lat' => 'required|numeric',
            'long' => 'required|numeric',
        ]);

        try {
			
			$imei = $request->imei_1;
			
            // ✅ Check in `customers` table
            $customer = Customer::with('retailer:id,username,email,mobile,plan')
				->where('imei_1', $imei)
                ->select('id', 'retailer_id', 'firebase_token', 'device_status')
                ->first();

            if (!$customer) {
                return $this->sendError('Customer does not exist. Please contact admin.', '', 200);
            }
			
			$customerId = Crypt::decryptString($customer->id);
			
			// Get count of today's entries for this customer
			$countToday = CustomerDeviceLocation::where('customer_id', $customerId)
				->whereDate('created_at', today())
				->count();

			if ($countToday >= 10) {
				return response()->json(['message' => 'Daily limit reached (10 entries per day)'], 200);
			}
			
			$activityData = [
				'imei' => $request->imei_1,
				'request' => $request->all()
			];
            ImeiActivityService::log('Add Location', auth()->user(), null, $activityData, 'customer');

            // Store location
            $location = CustomerDeviceLocation::create([
                'customer_id' => $customerId,
                'lat' => $request->lat,
                'long' => $request->long,
            ]);

            return response()->json(['message' => 'Location stored successfully', 'data' => $location], 200);
        } catch (\Exception $e) {
			$activityData = [
				'imei' => $request->imei_1,
				'request' => $e->getMessage()
			];
            ImeiActivityService::log('Add Location Error', auth()->user(), null, $activityData, 'customer');
            return response()->json(['message' => $e->getMessage()], 200);
        }
    }

    // Get locations
    public function index($encryptedId, Request $request)
    {
        try {
			$id = Crypt::decryptString($encryptedId);
		} catch (\Exception $e) {
			return response()->json(['message' => 'Invalid ID'], 200);
		}
		
		// Get date from request or default to today's date
		$date = $request->input('date', Carbon::now()->toDateString());
		
        $locations = CustomerDeviceLocation::where('customer_id', $id)
			->select('id','lat','long','created_at')
            ->whereNull('deleted_at')
			->whereDate('created_at', $date)
            ->paginate(10);

		return response()->json($locations);
    }

    // Soft delete location
    public function destroy($id)
    {
        try {
            $location = CustomerDeviceLocation::findOrFail($id);
            $location->delete(); // Soft delete
            return response()->json(['message' => 'Location deleted successfully'], 200);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Location not found'], 200);
        }
    }
}

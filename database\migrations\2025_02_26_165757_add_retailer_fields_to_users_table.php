<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddRetailerFieldsToUsersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('mobile')->nullable()->after('email');
            $table->text('address')->nullable()->after('mobile');
            $table->string('country')->nullable()->after('address');
            $table->string('state')->nullable()->after('country');
            $table->string('city')->nullable()->after('state');
            $table->string('pincode')->nullable()->after('city');
            $table->string('plan')->nullable()->after('pincode');
            $table->string('fcm_token')->nullable()->after('plan');
            $table->integer('total_keys')->default(0)->after('fcm_token');
            $table->decimal('price_per_key', 10, 2)->default(0)->after('total_keys');
            $table->decimal('total_price', 10, 2)->default(0)->after('price_per_key');
            $table->text('retailer_signature')->nullable()->after('total_price');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'mobile',
                'address',
                'country',
                'state',
                'city',
                'pincode',
                'plan',
                'fcm_token',
                'total_keys',
                'price_per_key',
                'total_price',
                'retailer_signature'
            ]);
        });
    }
}

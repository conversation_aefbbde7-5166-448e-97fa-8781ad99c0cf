<?php

namespace App\Http\Traits;
//use App\Models\Booking;
use App\User;

trait ApiGlobalFunctions
{

    /**
     * success response method.
     *
     * @return \Illuminate\Http\Response
     */
    public function sendResponse($result, $message)
    {
        $response = [
            'status' => true,
            'code' => 200,
            'message' => $message,
            'data' => $result

        ];
        return response()->json($response, 200);
    }

    /**
     * return error response.
     *
     * @return \Illuminate\Http\Response
     */
    public function sendError($error, $errorMessages = [], $code = 404)
    {
        $response = [
            'status' => false,
            'code' => 200,
            'message' => !empty($errorMessages) ? $errorMessages : $error,
            'data' => (object)[]
        ];
        return response()->json($response, $code);
    }

     public function sendErrorForVersion($error, $errorMessages = [], $code = 404)
    {
        $response = [
            'status' => false,
            'code' => 402,
            'message' => !empty($errorMessages) ? $errorMessages : $error,
            'data' => (object)[]
        ];
        return response()->json($response, $code);
    }

     public function sendErrorForAuth($error, $errorMessages = [], $code = 404)
    {
        $response = [
            'status' => false,
            'code' => 401,
            'message' => !empty($errorMessages) ? $errorMessages : $error,
            'data' => (object)[]
        ];
        return response()->json($response, $code);
    }

    public function messageDefault($label)
    {
        $msgArray = [
            'params_not_available' => 'This required parameter is not availble in the request',
            'signup_success' => 'You have been registered successfully. Please check your email for activate your account.',
            'signup_error' => 'The user could not be saved. Please, try again',
            'validate_error' => 'A validation error occurred',
            'invalid_login' => 'License key is incorrect',
            'invalid_token' => 'Invalid token',
            'invalid_csrf_token' => 'Invalid CSRF token',
            'invalid_request' => 'Invalid request',
            'invalid_account' => 'Invalid account, You are not authorize to access this',
            'invalid_access' => 'You are not authorize to access this',
            'not_verified' => 'Your account is not verified. Please check your email and verify them',
            'not_activated' => 'Your account is not activated, Please check contact admin',
            'verified_success' => 'Your account has verified successfully',
            'logout_success' => 'You have logged out successfully',
            'login_success' => 'You have logged in successfully',
            'forgot_success' => 'A verification code has been sent to your email, Please check your email for the code',
            'forgot_app_success' => 'New password has been sent to your registered email',
            'resend_otp_success' => 'New verification code generated and has been sent to your email',
            'profile_edit' => 'Your profile has been updated successfully.',
            'profile_get' => 'Your profile data',
            'password_update' => 'Your password has been updated successfully.',
            'token_not_match' => 'Token not match',
            'list_found' => 'List found.',
            'record_found' => 'Record found.',
            'list_not_found' => 'List not found.',
            'record_not_found' => 'Record not found.',
            'records_delete' => "Records has been deleted successfully.",
            'process_failed' => 'Your process failed. Please try again.',
            'record_exists' => 'Request data is already exists.',
            'save_records' => 'Record has been saved successfully.',
            'save_failed' => 'The user could not be saved. Please, try again.',
            'not_register_email' => 'Email address is not registered with us.',
            'not_active' => 'Your account has been deactivated, Please contact.',
            'change_password_success' => 'Your password changed successfully.',
            'oops' => 'Something went wrong',
            'feeback_submitted' => 'Feedback has been submited successfully',
        ];
        return isset($msgArray[$label]) ? $msgArray[$label] : $label;
    }
}

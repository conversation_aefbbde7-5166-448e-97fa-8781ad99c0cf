<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUserLicenseLogsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user_license_logs', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('licenses_id');
            $table->foreign('licenses_id')->references('id')->on('user_licenses')->onDelete('cascade');
            $table->string('price',255)->nullable(); 
            $table->string('activate_date',255)->nullable(); 
            $table->string('expiry_date',255)->nullable();
            $table->string('validity_days',255)->nullable();
            $table->boolean('status')->default(1);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('user_license_logs');
    }
}

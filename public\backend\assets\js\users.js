$(document).ready(function() {

    // Toggle filter section
    $('#toggleFilters').on('click', function() {
        var $filterSection = $('#filterSection');
        var $icon = $(this).find('i');

        if ($filterSection.is(':visible')) {
            $filterSection.slideUp();
            $icon.removeClass('fa-chevron-up').addClass('fa-chevron-down');
            $(this).html('<i class="fas fa-chevron-down"></i> Expand');
        } else {
            $filterSection.slideDown();
            $icon.removeClass('fa-chevron-down').addClass('fa-chevron-up');
            $(this).html('<i class="fas fa-chevron-up"></i> Collapse');
        }
    });

    // State-City dependency
    $('#filter_state_id').on('change', function() {
        var stateId = $(this).val();
        var $citySelect = $('#filter_city_id');

        // Clear city dropdown
        $citySelect.html('<option value="">All Cities</option>');

        if (stateId) {
            $.ajax({
                url: window.usersConfig.citiesRoute + "/" + stateId,
                type: 'GET',
                dataType: 'json',
                success: function(data) {
                    $.each(data, function(id, name) {
                        $citySelect.append('<option value="' + id + '">' + name + '</option>');
                    });

                    // Restore selected city if exists
                    var selectedCity = window.usersConfig.selectedCity;
                    if (selectedCity) {
                        $citySelect.val(selectedCity);
                    }
                },
                error: function() {
                    console.log('Error loading cities');
                }
            });
        }
    });

    // Trigger state change on page load if state is selected
    if ($('#filter_state_id').val()) {
        $('#filter_state_id').trigger('change');
    }

    // Bulk selection functionality
    $('#selectAll').on('change', function() {
        $('.user-checkbox').prop('checked', $(this).is(':checked'));
        toggleBulkActions();
    });

    $('.user-checkbox').on('change', function() {
        var totalCheckboxes = $('.user-checkbox').length;
        var checkedCheckboxes = $('.user-checkbox:checked').length;

        $('#selectAll').prop('checked', totalCheckboxes === checkedCheckboxes);
        toggleBulkActions();
    });

    function toggleBulkActions() {
        var checkedCount = $('.user-checkbox:checked').length;
        if (checkedCount > 0) {
            $('#bulkActionsBtn').show().text('Bulk Actions (' + checkedCount + ')');
            $('#exportSelectedBtn').show().html('<i class="fas fa-check-square text-warning"></i> Export Selected (' + checkedCount + ')');
        } else {
            $('#bulkActionsBtn').hide();
            $('#exportSelectedBtn').hide();
        }
    }

    // Bulk actions dropdown
    $('#bulkActionsBtn').on('click', function() {
        var selectedIds = $('.user-checkbox:checked').map(function() {
            return $(this).val();
        }).get();

        if (selectedIds.length === 0) {
            $.alert({
                title: 'Warning',
                content: 'Please select at least one user.',
                type: 'orange',
                icon: 'fa fa-warning'
            });
            return;
        }

        $.confirm({
            title: 'Bulk Actions',
            content: 'What would you like to do with the selected ' + selectedIds.length + ' users?',
            buttons: {
                activate: {
                    text: '<i class="fas fa-check"></i> Activate All',
                    btnClass: 'btn-success',
                    action: function() {
                        bulkUpdateStatus(selectedIds, 1);
                    }
                },
                deactivate: {
                    text: '<i class="fas fa-times"></i> Deactivate All',
                    btnClass: 'btn-danger',
                    action: function() {
                        bulkUpdateStatus(selectedIds, 0);
                    }
                },
                cancel: {
                    text: 'Cancel',
                    btnClass: 'btn-secondary'
                }
            }
        });
    });

    function bulkUpdateStatus(userIds, status) {
        $.ajax({
            url: window.usersConfig.bulkUpdateRoute,
            type: 'POST',
            data: {
                user_ids: userIds,
                status: status,
                _token: $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                $.alert({
                    title: 'Success',
                    content: response.message,
                    type: 'green',
                    icon: 'fa fa-check',
                    onDestroy: function() {
                        location.reload();
                    }
                });
            },
            error: function(xhr) {
                var errorMessage = 'An error occurred while updating users.';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }

                $.alert({
                    title: 'Error',
                    content: errorMessage,
                    type: 'red',
                    icon: 'fa fa-warning'
                });
            }
        });
    }

    // Export functionality
    var currentExportData = {};

        function showExportSummary(format = 'xlsx', selectedOnly = false) {
            var form = $('form[action="' + window.usersConfig.indexRoute + '"]');
            var formData = form.serialize();

            // Calculate total users to export
            var totalUsers = selectedOnly ? $('.user-checkbox:checked').length : window.usersConfig.totalUsers;

            if (selectedOnly && totalUsers === 0) {
                $.alert({
                    title: 'Warning',
                    content: 'Please select at least one user to export.',
                    type: 'orange',
                    icon: 'fa fa-warning'
                });
                return;
            }

            // Store export data for later use
            currentExportData = {
                format: format,
                selectedOnly: selectedOnly,
                formData: formData
            };

            // Update modal content
            $('#exportTotalUsers').text(totalUsers);
            $('#exportFormat').text(format.toUpperCase());

            // Show applied filters
            var filters = [];
            var params = new URLSearchParams(formData);

            if (params.get('keyword')) {
                filters.push('Keyword: "' + params.get('keyword') + '"');
            }
            if (params.get('state')) {
                var stateName = $('#filter_state_id option:selected').text();
                filters.push('State: ' + stateName);
            }
            if (params.get('city')) {
                var cityName = $('#filter_city_id option:selected').text();
                filters.push('City: ' + cityName);
            }
            if (params.get('status') !== null && params.get('status') !== '') {
                var statusText = params.get('status') === '1' ? 'Active' : 'Inactive';
                filters.push('Status: ' + statusText);
            }
            if (params.get('date_from')) {
                filters.push('Date From: ' + params.get('date_from'));
            }
            if (params.get('date_to')) {
                filters.push('Date To: ' + params.get('date_to'));
            }
            if (selectedOnly) {
                filters.push('Export Type: Selected Users Only');
            }

            var filtersHtml = filters.length > 0 ?
                '<ul class="list-unstyled mb-0">' + filters.map(f => '<li><i class="fas fa-check text-success"></i> ' + f + '</li>').join('') + '</ul>' :
                '<small class="text-muted">No filters applied - exporting all users</small>';

            $('#appliedFilters').html(filtersHtml);

            // Show modal
            $('#exportSummaryModal').modal('show');
        }

    function performExport() {
        var format = currentExportData.format;
        var selectedOnly = currentExportData.selectedOnly;
        var formData = currentExportData.formData;

        // Hide modal
        $('#exportSummaryModal').modal('hide');

        // Show loading state
        var exportBtn = $('#exportBtn');
        var originalText = exportBtn.html();
        exportBtn.html('<i class="fas fa-spinner fa-spin"></i> Exporting...').prop('disabled', true);

        // Create a temporary form to submit the export request
        var exportForm = $('<form>', {
            'method': 'GET',
            'action': window.usersConfig.exportRoute
        });

        // Add all filter parameters to the form
        var params = new URLSearchParams(formData);
        params.forEach(function(value, key) {
            exportForm.append($('<input>', {
                'type': 'hidden',
                'name': key,
                'value': value
            }));
        });

        // Add format parameter
        exportForm.append($('<input>', {
            'type': 'hidden',
            'name': 'format',
            'value': format
        }));

        // Add selected users if exporting selected only
        if (selectedOnly) {
            var selectedIds = $('.user-checkbox:checked').map(function() {
                return $(this).val();
            }).get();

            selectedIds.forEach(function(id) {
                exportForm.append($('<input>', {
                    'type': 'hidden',
                    'name': 'selected_ids[]',
                    'value': id
                }));
            });
        }

        // Submit the form
        $('body').append(exportForm);
        exportForm.submit();
        exportForm.remove();

        // Reset button state after a short delay
        setTimeout(function() {
            exportBtn.html(originalText).prop('disabled', false);
        }, 2000);
    }

    // Export button handlers
    $('#exportBtn, #exportExcelBtn').on('click', function(e) {
        e.preventDefault();
        showExportSummary('xlsx');
    });

    $('#exportCsvBtn').on('click', function(e) {
        e.preventDefault();
        showExportSummary('csv');
    });

    $('#exportSelectedBtn').on('click', function(e) {
        e.preventDefault();
        showExportSummary('xlsx', true);
    });

    // Confirm export button in modal
    $('#confirmExportBtn').on('click', function() {
        performExport();
    });

    // Search form enhancement
    $('form').on('submit', function() {
        var submitBtn = $(this).find('button[type="submit"]');
        var originalText = submitBtn.html();
        submitBtn.html('<i class="fas fa-spinner fa-spin"></i> Searching...').prop('disabled', true);
    });

    // Highlight search terms
    function highlightSearchTerms() {
        var keyword = window.usersConfig.searchKeyword || '';
        if (keyword) {
            $('table tbody td').each(function() {
                var text = $(this).text();
                if (text.toLowerCase().includes(keyword.toLowerCase())) {
                    var highlightedText = text.replace(
                        new RegExp('(' + keyword + ')', 'gi'),
                        '<mark>$1</mark>'
                    );
                    $(this).html(highlightedText);
                }
            });
        }
    }

    // Apply highlighting after page load
    highlightSearchTerms();

    // Date range validation
    $('#date_from, #date_to').on('change', function() {
        var dateFrom = $('#date_from').val();
        var dateTo = $('#date_to').val();

        if (dateFrom && dateTo && dateFrom > dateTo) {
            alert('Date From cannot be greater than Date To');
            $(this).val('');
        }
    });

    // Change Password Modal
    $('.changePassword').on('click', function() {
        var userId = $(this).data('user-id');
        var userName = $(this).data('user-name');

        $('#userId').val(userId);
        $('#userName').val(userName);
        $('#newPassword').val('');
        $('#confirmPassword').val('');
        $('#changePasswordModal').modal('show');
    });

    // Toggle password visibility
    $('#togglePassword').on('click', function() {
        var passwordField = $('#newPassword');
        var icon = $(this).find('i');

        if (passwordField.attr('type') === 'password') {
            passwordField.attr('type', 'text');
            icon.removeClass('fa-eye').addClass('fa-eye-slash');
        } else {
            passwordField.attr('type', 'password');
            icon.removeClass('fa-eye-slash').addClass('fa-eye');
        }
    });

    // Handle change password form submission
    $('#changePasswordForm').on('submit', function(e) {
        e.preventDefault();

        var newPassword = $('#newPassword').val();
        var confirmPassword = $('#confirmPassword').val();
        var userId = $('#userId').val();

        // Validate passwords match
        if (newPassword !== confirmPassword) {
            $.alert({
                title: 'Error',
                content: 'Passwords do not match!',
                type: 'red',
                icon: 'fa fa-warning'
            });
            return;
        }

        // Validate password length
        if (newPassword.length < 6) {
            $.alert({
                title: 'Error',
                content: 'Password must be at least 6 characters long!',
                type: 'red',
                icon: 'fa fa-warning'
            });
            return;
        }

        // Submit password change
        $.ajax({
            url: window.usersConfig.changePasswordRoute,
            type: 'POST',
            data: {
                user_id: userId,
                password: newPassword,
                password_confirmation: confirmPassword,
                _token: $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                $('#changePasswordModal').modal('hide');
                $.alert({
                    title: 'Success',
                    content: 'Password changed successfully!',
                    type: 'green',
                    icon: 'fa fa-check'
                });
            },
            error: function(xhr) {
                var errors = xhr.responseJSON;
                var errorMessage = 'An error occurred while changing password.';

                if (errors && errors.message) {
                    errorMessage = errors.message;
                } else if (errors && errors.errors) {
                    errorMessage = Object.values(errors.errors).join('<br>');
                }

                $.alert({
                    title: 'Error',
                    content: errorMessage,
                    type: 'red',
                    icon: 'fa fa-warning'
                });
            }
        });
    });

    // Transfer Keys Modal
    $('.transferKeys').on('click', function() {
        var userId = $(this).data('user-id');
        var userName = $(this).data('user-name');
        var availableKeys = $(this).data('available-keys');

        $('#fromUserId').val(userId);
        $('#fromUserName').val(userName);
        $('#availableKeys').val(availableKeys + ' keys');

        // Reset form
        $('#transferKeysForm')[0].reset();
        $('#fromUserId').val(userId); // Reset this again as form reset clears it
        $('#fromUserName').val(userName);
        $('#availableKeys').val(availableKeys + ' keys');
        $('#transferSummary').hide();

        // Set default target user to current user
        //$('#targetUser').val(userId);

        $('#transferKeysModal').modal('show');
    });

    // Transfer form validation and summary
    $('#transferQuantity, #transferAction').on('change keyup', function() {
        updateTransferSummary();
    });

    function updateTransferSummary() {
        var quantity = $('#transferQuantity').val();
        var action = $('#transferAction').val();
        var userName = $('#fromUserName').val();
        var availableKeys = parseInt($('#availableKeys').val());

        if (quantity && action) {
            var actionText = action === 'add' ? 'Add' : 'Subtract';

            var summaryHtml = '<ul class="mb-0">';
            summaryHtml += '<li><strong>User:</strong> ' + userName + '</li>';
            summaryHtml += '<li><strong>Action:</strong> ' + actionText + ' ' + quantity + ' keys</li>';

            if (action === 'subtract' && parseInt(quantity) > availableKeys) {
                summaryHtml += '<li class="text-danger"><strong>Warning:</strong> Not enough available keys!</li>';
            } else if (action === 'add') {
                summaryHtml += '<li class="text-success"><strong>Result:</strong> User will have ' + (availableKeys + parseInt(quantity)) + ' keys</li>';
            } else {
                summaryHtml += '<li class="text-info"><strong>Result:</strong> User will have ' + (availableKeys - parseInt(quantity)) + ' keys</li>';
            }

            summaryHtml += '</ul>';

            $('#summaryContent').html(summaryHtml);
            $('#transferSummary').show();
        } else {
            $('#transferSummary').hide();
        }
    }

    // Handle transfer form submission
    $('#transferKeysForm').on('submit', function(e) {
        e.preventDefault();

        var userId = $('#fromUserId').val();
        var quantity = $('#transferQuantity').val();
        var action = $('#transferAction').val();
        //var targetUser = $('#targetUser').val();
        var note = $('#transferNote').val();
        var availableKeys = parseInt($('#availableKeys').val());

        // Validation
        if (!action || !quantity) {
            $.alert({
                title: 'Error',
                content: 'Please fill in all required fields!',
                type: 'red',
                icon: 'fa fa-warning'
            });
            return;
        }

        if (action === 'subtract' && parseInt(quantity) > availableKeys) {
            $.alert({
                title: 'Error',
                content: 'User does not have enough available keys!',
                type: 'red',
                icon: 'fa fa-warning'
            });
            return;
        }

        // Confirm transfer
        var actionText = action === 'add' ? 'add' : 'subtract';
        var confirmMessage = 'Are you sure you want to ' + actionText + ' ' + quantity + ' keys for this user?';

        $.confirm({
            title: 'Confirm Transfer',
            content: confirmMessage,
            icon: 'fa fa-exchange-alt',
            type: 'blue',
            buttons: {
                confirm: {
                    text: 'Yes, Transfer',
                    btnClass: 'btn-primary',
                    action: function() {
                        performTransfer(userId, quantity, action, note);
                    }
                },
                cancel: {
                    text: 'Cancel',
                    btnClass: 'btn-secondary'
                }
            }
        });
    });

    function performTransfer(userId, quantity, action, note) {
        $.ajax({
            url: window.usersConfig.updateKeysRoute + "/" + userId,
            type: 'POST',
            data: {
                action: action,
                quantity: quantity,
                note: note,
                _token: $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                $('#transferKeysModal').modal('hide');
                $.alert({
                    title: 'Success',
                    content: 'Keys transferred successfully!',
                    type: 'green',
                    icon: 'fa fa-check',
                    onDestroy: function() {
                        location.reload();
                    }
                });
            },
            error: function(xhr) {
                var errorMessage = 'An error occurred while transferring keys.';

                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                } else if (xhr.responseJSON && xhr.responseJSON.errors) {
                    errorMessage = Object.values(xhr.responseJSON.errors).join('<br>');
                }

                $.alert({
                    title: 'Error',
                    content: errorMessage,
                    type: 'red',
                    icon: 'fa fa-warning'
                });
            }
        });
    }
});

    $(document).on('click', '.updateStatus', function(event) {
        event.preventDefault();
        var _this = $(this);
        var title = _this.data('title');
        var value = _this.attr('data-value') == 1 ? 0 : 1 ;
        var column = _this.data('column');
        console.log(column);
        var message = "Are you sure want to active this user?";
        if(column == "status"){
            if(_this.attr('data-value') == 1){
                message = 'Are you sure want to in-active this user?';
            }else{
                message = 'Are you sure want to active this user?';
            }
        }
        $.confirm({
            title: 'Alert',
            content: message,
            icon: 'fa fa-exclamation-circle',
            animation: 'scale',
            closeAnimation: 'scale',
            opacity: 0.5,
            theme: 'supervan',
            buttons: {
                'confirm': {
                    text: 'Yes',
                    btnClass: 'btn-blue',
                    action: function() {
                        $.ajax({
                            url: _this.data('url'),
                            data: {status: value},
                            type: 'POST',
                            dataType: "json",
                            headers: {
                                "accept": "application/json",
                                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                                },
                            success: function (data) {
                                    if(data.success==true){
                                        if(value == 0){
                                            _this.removeClass("btn-success");
                                            _this.addClass("label-danger");
                                            _this.html("In-Active");
                                            _this.attr("data-value",0);
                                        }else{
                                            _this.removeClass("label-danger");
                                        //   _this.removeClass("updateStatus");
                                            _this.addClass("btn-success");
                                            _this.html("Active");
                                            _this.attr("data-value",1);
                                        }


                                    $.alert({
                                        columnClass: 'medium',
                                        title: 'Success',
                                        icon: 'fa fa-check',
                                        type: 'green',
                                        content:  data.message,
                                        });
                                    }else{
                                            $.alert({
                                                columnClass: 'col-md-8',
                                                title: 'Error',
                                                icon:  'fa fa-warning',
                                                type:  'red',
                                                content: data.message,
                                            });
                                    }
                                    },
                                    error: function (xhr, ajaxOptions, thrownError) {
                                        var errors = JSON.parse(xhr.responseText);
                                        console.log(errors);
                                        var errs = '';
                                        $.each( errors.errors, function( key, value ) {
                                            console.log( key + ": " + value );
                                            errs += value;
                                            errs += "<br>";
                                        });
                                    $.alert({
                                    // columnClass: 'medium',
                                        title: errors.message,
                                        icon:  'fa fa-warning',
                                        type:  'red',
                                            content:  errs,
                                        });
                                    }
                        });
                    }
                },
                cancelAction: {
                    text: 'Cancel'
                }
            }
        });
    });

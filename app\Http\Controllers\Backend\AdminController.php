<?php

namespace App\Http\Controllers\Backend;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller;
use App\Http\Requests\Backend\UpdateUserProfile;
use App\Models\Customer;
use Illuminate\Support\Facades\Auth;
use App\Models\User;
use App\Models\DeviceInfo;

class AdminController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function index()
    {

        $user = Auth::user();

        // Step 1: Get all downline user IDs recursively
        $user->load('downlines'); // eager load to reduce queries
        $downlineIds = getAllDescendantUserIds($user);

        // Step 2: Get count of users by role
        $roleStats = User::whereIn('id', $downlineIds)
            ->with('roles')
            ->get()
            ->groupBy(function($user) {
                return $user->getRoleNames()->first(); // assuming 1 role per user
            })
            ->map(function($group) {
                return $group->count();
            });

        $retailerIds = User::whereIn('id', $downlineIds)
            ->role('Retailer')
            ->pluck('id');

		/* $deviceInfo = DeviceInfo::selectRaw("
			COUNT(*) as total_installed_imei,
			SUM(CASE WHEN deleted_at IS NOT NULL THEN 1 ELSE 0 END) as deleted_imei,
			SUM(CASE WHEN deleted_at IS NULL And DATE(created_at) = CURDATE() THEN 1 ELSE 0 END) as today_installed_imei,
			SUM(CASE WHEN DATE(deleted_at) = CURDATE() THEN 1 ELSE 0 END) as today_deleted_imei
		")->withTrashed()->first(); */

        $customerStats = Customer::whereIn('retailer_id', $retailerIds)
            ->selectRaw("
                COUNT(*) as total_installed_imei,
                SUM(CASE WHEN deleted_at IS NOT NULL THEN 1 ELSE 0 END) as deleted_imei,
                SUM(CASE WHEN deleted_at IS NULL AND DATE(created_at) = CURDATE() THEN 1 ELSE 0 END) as today_installed_imei,
                SUM(CASE WHEN DATE(deleted_at) = CURDATE() THEN 1 ELSE 0 END) as today_deleted_imei
            ")
            ->withTrashed()
            ->first();


		$countData = [
			'total_installed_imei' => $customerStats->total_installed_imei,
			'total_deleted_imei' => $customerStats->deleted_imei,
			'today_installed_imei' => $customerStats->today_installed_imei,
			'today_deleted_imei' => $customerStats->today_deleted_imei,
		];

        $keyStats = [
            'total_keys' => $user->total_keys,
            'used_keys' => $user->used_keys,
            'remaining_keys' => $user->remaining_keys, // uses accessor
        ];
		
        return view('backend/admin-dashboard/index',compact('countData', 'roleStats', 'keyStats'));
    }

    /**
     * Show the admin profile.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function profile()
    {
        $user = User::where('id',Auth::user()->id)->first();
		
        return view('backend/admin-dashboard/profile',compact('user'));
		
    }
    /**
     * Update the specified resource in storage.
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function updateProfile(UpdateUserProfile $request)
    {
        try 
        {  
          $input = $request->except(['username','email']); 
          $user = Auth::user();
          
          /*Save image */
          if($request->file('image')){
              if(\File::exists(url('uploads/users/'.$user->image))){
                  \File::delete(url('uploads/users/'.$user->image));
              }
              $image = 'users_'.time().'.'.$request->image->extension(); 
              $destinationPath = 'uploads/users';
              $request->image->move($destinationPath,$image);         
              $input['image'] = $image;

          }         

          $user->fill($input);
          $user->push();          
          return back()->with('alert-success','Profile updated successfully');
        } catch (\Illuminate\Database\QueryException $e) {
           return back()->with('alert-danger',$e->getMessage())->withInput();
        }
    }

    /**
     * Show the admin change password.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function getPassword()
    {
		//dd(\Hash::make('867&qc%7np:>'));
        $user = User::where('id',Auth::user()->id)->with(['userProfile'])->first();
        return view('backend/dashboards/password',compact('user'));
    }
}

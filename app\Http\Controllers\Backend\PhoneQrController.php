<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\NewPhoneQr;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;
use App\Models\Setting;
use App\Models\ApiKey;

class PhoneQrController extends Controller
{
    
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $data = NewPhoneQr::orderBy('id', 'desc')->paginate(20);
		$settings = Setting::pluck('value', 'key')->toArray();
		$companies = ApiKey::where(['is_active'=>1])->whereNotNull('ip_address')->pluck('company_name','company_name')->toArray();
        return view('backend.phone_qr.index', compact('data','companies','settings'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {		
		if ($request->hasFile('image')) {
			$request->validate([
				'image' => 'required|image|max:2048',
			]);

			//delete_directory('storage/qr_code'); // Delete all files in the directory

			\Storage::deleteDirectory('public/qr_code'); // Delete the directory itself

			$qrCodePath = $request->file('image')->store('qr_code', 'public');

			// Update all records in the table with the new QR code and fields
			NewPhoneQr::query()->update([
				'image' => $qrCodePath, // Update QR code for all rows
			]);
		}
		
		if ($request->hasFile('lockmaster_qr_code')) {
			
			$request->validate([
				'lockmaster_qr_code' => 'required|image|max:2048',
			]);
			
			//save qr code for customers
			$customerQrCode = $this->uploadFile($request->file('lockmaster_qr_code'), 'customers/qr-code');
			Setting::setValue('lockmaster_qr_code', $customerQrCode);
		}
        
        return redirect()->route('admin.phone-qr')->with('alert-success', 'QR code processed successfully.');
    }
	
	/**
	 * Uploads a file with a timestamp-based name
	 */
	private function uploadFile($file, $path)
	{
		$fileName = md5($file->getFilename() . time()) . '.' . $file->getClientOriginalExtension();
		$file->storeAs($path, $fileName, 'public');
		
		return $fileName;
	}
}

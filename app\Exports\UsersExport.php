<?php

namespace App\Exports;

use App\Models\User;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use Illuminate\Support\Facades\Auth;

class UsersExport implements FromQuery, WithHeadings, WithMapping, WithStyles, ShouldAutoSize, WithColumnFormatting
{
    protected $filters;

    public function __construct($filters = [])
    {
        $this->filters = $filters;
    }

    /**
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function query()
    {
        $query = User::with(['stateDetail', 'cityDetail', 'roles'])
                    ->where('admin_id', Auth::id());

        // Apply filters
        if (!empty($this->filters['keyword'])) {
            $keyword = $this->filters['keyword'];
            $query->where(function($q) use ($keyword) {
                $q->where('username', 'LIKE', "%{$keyword}%")
                  ->orWhere('email', 'LIKE', "%{$keyword}%")
                  ->orWhere('mobile', 'LIKE', "%{$keyword}%")
                  ->orWhere('company_name', 'LIKE', "%{$keyword}%");
            });
        }

        if (!empty($this->filters['state'])) {
            $query->where('state', $this->filters['state']);
        }

        if (!empty($this->filters['city'])) {
            $query->where('city', $this->filters['city']);
        }

        if (isset($this->filters['status']) && $this->filters['status'] !== '') {
            $query->where('status', $this->filters['status']);
        }

        if (!empty($this->filters['date_from'])) {
            $query->whereDate('created_at', '>=', $this->filters['date_from']);
        }

        if (!empty($this->filters['date_to'])) {
            $query->whereDate('created_at', '<=', $this->filters['date_to']);
        }

        return $query->latest();
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            'S.No',
            'Name',
            'Email',
            'Mobile',
            'Company Name',
            'State',
            'City',
            'Address',
            'Plan',
            'Total Keys',
            'Used Keys',
            'Remaining Keys',
            'Price Per Key',
            'Total Price',
            'Status',
            'Role',
            'Registration Date',
            'Last Updated'
        ];
    }

    /**
     * @param mixed $user
     * @return array
     */
    public function map($user): array
    {
        static $counter = 0;
        $counter++;

        return [
            $counter,
            $user->username,
            $user->email,
            $user->mobile,
            $user->company_name ?? 'N/A',
            $user->stateDetail->name ?? 'N/A',
            $user->cityDetail->name ?? 'N/A',
            $user->address ?? 'N/A',
            ucwords(str_replace('_', ' ', $user->plan ?? 'N/A')),
            $user->total_keys ?? 0,
            $user->used_keys ?? 0,
            max(($user->total_keys ?? 0) - ($user->used_keys ?? 0), 0),
            $user->price_per_key ?? 0,
            $user->total_price ?? 0,
            $user->status == 1 ? 'Active' : 'Inactive',
            $user->getRoleNames()->first() ?? 'N/A',
            $user->created_at ? $user->created_at->format('Y-m-d H:i:s') : 'N/A',
            $user->updated_at ? $user->updated_at->format('Y-m-d H:i:s') : 'N/A'
        ];
    }

    /**
     * @param Worksheet $sheet
     * @return array
     */
    public function styles(Worksheet $sheet)
    {
        return [
            // Style the first row as bold text
            1 => ['font' => ['bold' => true, 'size' => 12]],
            
            // Set background color for header
            'A1:R1' => [
                'fill' => [
                    'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                    'startColor' => ['argb' => 'FF4472C4']
                ],
                'font' => [
                    'color' => ['argb' => 'FFFFFFFF'],
                    'bold' => true
                ]
            ],
        ];
    }

    /**
     * @return array
     */
    public function columnFormats(): array
    {
        return [
            //'M' => NumberFormat::FORMAT_CURRENCY_USD, // Price Per Key
            //'N' => NumberFormat::FORMAT_CURRENCY_USD, // Total Price
            'Q' => NumberFormat::FORMAT_DATE_DATETIME, // Registration Date
            'R' => NumberFormat::FORMAT_DATE_DATETIME, // Last Updated
        ];
    }
}
